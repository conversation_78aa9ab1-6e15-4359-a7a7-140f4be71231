import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Linkedin, Twitter, Facebook, Youtube, Instagram, ExternalLink } from 'lucide-react';
import { createPageUrl } from '@/utils';
import { safeParseJSONField } from '@/utils/dataParser';

export default function EducatorCard({ educator, compact = false }) {
  // Parse JSON string fields safely
  const expertise = safeParseJSONField(educator.expertise, []);
  const socialMedia = safeParseJSONField(educator.social_media, {});

  const socialIcons = {
    linkedin: { icon: Linkedin, color: "text-[#0077B5]" },
    twitter: { icon: Twitter, color: "text-[#1DA1F2]" },
    facebook: { icon: Facebook, color: "text-[#4267B2]" },
    youtube: { icon: Youtube, color: "text-[#FF0000]" },
    instagram: { icon: Instagram, color: "text-[#E1306C]" }
  };

  const renderSocialLinks = () => {
    if (!socialMedia || Object.keys(socialMedia).length === 0) return null;

    return (
      <div className="flex space-x-2 mt-2">
        {Object.entries(socialMedia).map(([platform, url]) => {
          if (!url) return null;
          const { icon: Icon, color } = socialIcons[platform] || {};
          if (!Icon) return null;

          return (
            <a
              key={platform}
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className={`${color} hover:opacity-80 transition-opacity`}
            >
              <Icon className="w-4 h-4" />
            </a>
          );
        })}
      </div>
    );
  };
  
  if (compact) {
    return (
      <div className="flex items-center gap-3 p-3">
        <Avatar className="h-10 w-10">
          <AvatarImage src={educator.headshot_url} alt={educator.full_name} />
          <AvatarFallback>{educator.full_name[0]}</AvatarFallback>
        </Avatar>
        <div>
          <div className="font-medium text-sm">{educator.full_name}</div>
          <div className="text-xs text-gray-500">{educator.title}</div>
        </div>
      </div>
    );
  }

  return (
    <Card className="overflow-hidden h-full flex flex-col">
      <div className="aspect-square relative overflow-hidden">
        <img 
          src={educator.headshot_url} 
          alt={educator.full_name} 
          className="w-full h-full object-cover"
          onError={(e) => {
            e.target.src = 'https://placehold.co/400x400?text=Educator';
          }}
        />
        {educator.featured && (
          <Badge className="absolute top-2 right-2 bg-[#8890ea]">Featured</Badge>
        )}
      </div>
      <CardContent className="pt-4 flex-grow">
        <h3 className="font-bold text-lg">{educator.full_name}</h3>
        <p className="text-sm text-gray-500">{educator.title}</p>
        {educator.company && (
          <p className="text-sm flex items-center gap-1 mt-1">
            {educator.company}
            {educator.company_website && (
              <a 
                href={educator.company_website} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-gray-400 hover:text-gray-600"
              >
                <ExternalLink className="h-3 w-3" />
              </a>
            )}
          </p>
        )}
        {renderSocialLinks()}
        {expertise && expertise.length > 0 && (
          <div className="mt-3">
            <div className="text-xs text-gray-500 mb-1">Expertise:</div>
            <div className="flex flex-wrap gap-1">
              {expertise.map((area, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {area}
                </Badge>
              ))}
            </div>
          </div>
        )}
        <p className="mt-3 text-sm line-clamp-3">{educator.bio}</p>
      </CardContent>
      <CardFooter className="border-t pt-4">
        <Button 
          variant="outline" 
          className="w-full"
          onClick={() => window.location.href = createPageUrl(`EducatorProfile?id=${educator.id}`)}
        >
          View Profile
        </Button>
      </CardFooter>
    </Card>
  );
}
