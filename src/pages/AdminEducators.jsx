import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { toast } from "@/components/ui/use-toast";
import {
  Search,
  Edit2,
  Trash2,
  Plus,
  Briefcase,
  User,
  UserPlus,
  Users,
  Star,
  ExternalLink
} from 'lucide-react';
import { Educator } from '@/api/entities';
import { Course } from '@/api/entities';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { createPageUrl } from '@/utils';
import { safeParseJSONField } from '@/utils/dataParser';

export default function AdminEducators() {
  const [educators, setEducators] = useState([]);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [educatorToDelete, setEducatorToDelete] = useState(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [educatorData, courseData] = await Promise.all([
        Educator.list(),
        Course.list()
      ]);
      setEducators(educatorData || []);
      setCourses(courseData || []);
    } catch (error) {
      console.error('Error loading data:', error);
      toast({
        title: "Error loading data",
        description: "Failed to load educators. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddEducator = () => {
    window.location.href = createPageUrl('AdminEducatorCreate');
  };

  const handleEditEducator = (educator) => {
    window.location.href = createPageUrl(`AdminEducatorEdit?id=${educator.id}`);
  };

  const confirmDeleteEducator = (educator) => {
    setEducatorToDelete(educator);
    setShowConfirmDialog(true);
  };

  const handleDeleteEducator = async () => {
    if (!educatorToDelete) return;
    
    try {
      // Check if educator is used in any courses
      const educatorCourses = courses.filter(course => 
        course.educator && course.educator.id === educatorToDelete.id
      );
      
      if (educatorCourses.length > 0) {
        toast({
          title: "Cannot delete educator",
          description: `This educator is associated with ${educatorCourses.length} course(s). Please reassign these courses first.`,
          variant: "destructive"
        });
        setShowConfirmDialog(false);
        return;
      }
      
      await Educator.delete(educatorToDelete.id);
      toast({
        title: "Educator deleted",
        description: "The educator has been deleted successfully."
      });
      
      setEducators(educators.filter(e => e.id !== educatorToDelete.id));
      setShowConfirmDialog(false);
    } catch (error) {
      console.error('Error deleting educator:', error);
      toast({
        title: "Error deleting educator",
        description: error.message || "An error occurred while deleting the educator.",
        variant: "destructive"
      });
    }
  };

  const filteredEducators = educators.filter(educator =>
    educator.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    educator.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    educator.company?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Educators</h1>
            <p className="text-gray-500">Manage course instructors and experts</p>
          </div>
          <Button onClick={handleAddEducator} className="bg-[#8890ea] hover:bg-[#7a7dd4] text-white">
            <UserPlus className="w-4 h-4 mr-2" />
            Add Educator
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Total Educators
              </CardTitle>
              <Users className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{educators.length}</div>
              <div className="text-sm text-gray-500">
                {educators.filter(e => e.featured).length} featured educators
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Courses Created
              </CardTitle>
              <Briefcase className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{courses.length}</div>
              <div className="text-sm text-gray-500">
                By {new Set(courses.map(c => c.educator?.id).filter(Boolean)).size} educators
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Active Educators
              </CardTitle>
              <User className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {educators.filter(e => e.status === 'active').length}
              </div>
              <div className="text-sm text-gray-500">
                Ready to create courses
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col md:flex-row gap-4 justify-between">
              <CardTitle>All Educators</CardTitle>
              <div className="relative w-full md:w-64">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  placeholder="Search educators..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-4 text-gray-500">
                Loading educators...
              </div>
            ) : filteredEducators.length === 0 ? (
              <div className="text-center py-4 text-gray-500">
                No educators found. Add your first educator to get started.
              </div>
            ) : (
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Educator</TableHead>
                      <TableHead>Company</TableHead>
                      <TableHead>Expertise</TableHead>
                      <TableHead>Courses</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEducators.map((educator) => {
                      const educatorCourses = courses.filter(
                        course => course.educator && course.educator.id === educator.id
                      );
                      
                      return (
                        <TableRow key={educator.id}>
                          <TableCell>
                            <div className="flex items-center gap-3">
                              <Avatar>
                                <AvatarImage src={educator.headshot_url} />
                                <AvatarFallback>{educator.full_name[0]}</AvatarFallback>
                              </Avatar>
                              <div>
                                <div className="font-medium flex items-center gap-1">
                                  {educator.full_name}
                                  {educator.featured && (
                                    <Star className="h-3.5 w-3.5 text-amber-500" />
                                  )}
                                </div>
                                <div className="text-sm text-gray-500">{educator.title}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            {educator.company && (
                              <div className="flex items-center gap-1">
                                {educator.company}
                                {educator.company_website && (
                                  <a 
                                    href={educator.company_website} 
                                    target="_blank" 
                                    rel="noopener noreferrer"
                                    className="text-gray-400 hover:text-gray-600"
                                  >
                                    <ExternalLink className="h-3.5 w-3.5" />
                                  </a>
                                )}
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className="flex flex-wrap gap-1">
                              {(() => {
                                const expertise = safeParseJSONField(educator.expertise, []);
                                return (
                                  <>
                                    {expertise.slice(0, 3).map(area => (
                                      <Badge key={area} variant="outline">
                                        {area}
                                      </Badge>
                                    ))}
                                    {expertise.length > 3 && (
                                      <Badge variant="outline">
                                        +{expertise.length - 3} more
                                      </Badge>
                                    )}
                                  </>
                                );
                              })()}
                            </div>
                          </TableCell>
                          <TableCell>{educatorCourses.length}</TableCell>
                          <TableCell>
                            <Badge 
                              className={
                                educator.status === 'active' 
                                  ? 'bg-green-100 text-green-800' 
                                  : 'bg-gray-100 text-gray-800'
                              }
                            >
                              {educator.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => handleEditEducator(educator)}
                              >
                                <Edit2 className="h-4 w-4" />
                              </Button>
                              <Button 
                                variant="ghost" 
                                size="sm" 
                                className="text-red-600"
                                onClick={() => confirmDeleteEducator(educator)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Educator</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {educatorToDelete?.full_name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteEducator}
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
