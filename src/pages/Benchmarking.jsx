
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { User } from '@/api/entities';
import { BenchmarkData } from '@/api/entities';
import { MedSpaAccount } from '@/api/entities';
import { parseBenchmarkData, safeGet } from '@/utils/dataParser';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer, 
  LineChart,
  Line,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import { Download, Info, AlertTriangle } from 'lucide-react';
import PercentileIndicator from '../components/benchmarking/PercentileIndicator';
import BenchmarkComparison from '../components/benchmarking/BenchmarkComparison';
import IndustryTrendChart from '../components/benchmarking/IndustryTrendChart';

export default function Benchmarking() {
  const [period, setPeriod] = useState("90days");
  const [region, setRegion] = useState("all");
  const [practiceSize, setPracticeSize] = useState("all");
  const [benchmarkData, setBenchmarkData] = useState(null);
  const [businessData, setBusinessData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState(null);

  useEffect(() => {
    loadUserAndData();
  }, []);

  useEffect(() => {
    if (user) {
      loadBenchmarkData();
    }
  }, [period, region, practiceSize, user]);

  const loadUserAndData = async () => {
    try {
      const userData = await User.me();
      setUser(userData);
      
      // Get business account data
      if (userData.account_id) {
        const accounts = await MedSpaAccount.filter({ id: userData.account_id });
        if (accounts.length > 0) {
          setBusinessData(accounts[0]);
        }
      }
    } catch (error) {
      console.error("Error loading user data:", error);
    }
  };

  const loadBenchmarkData = async () => {
    setLoading(true);
    try {
      // In a production app, this would filter benchmarks based on selected period/region/size
      const benchmarks = await BenchmarkData.filter({ data_period: period });

      // Use the most recent benchmark data if available
      if (benchmarks.length > 0) {
        const mostRecent = benchmarks.sort((a, b) =>
          new Date(b.report_date) - new Date(a.report_date)
        )[0];

        // Parse JSON string fields safely
        const processedData = parseBenchmarkData(mostRecent);
        setBenchmarkData(processedData);
      } else {
        // If no real data, use sample data
        setBenchmarkData(getSampleBenchmarkData());
      }
    } catch (error) {
      console.error("Error loading benchmark data:", error);
      // Fallback to sample data
      setBenchmarkData(getSampleBenchmarkData());
    } finally {
      setLoading(false);
    }
  };





  // Helper function to safely access nested benchmark data properties
  const safeGetBenchmarkValue = (path, defaultValue = 0) => {
    return safeGet(benchmarkData, `metrics.${path}`, defaultValue);
  };

  // Sample data function (would be replaced with actual data in production)
  const getSampleBenchmarkData = () => {
    return {
      report_date: new Date().toISOString().split('T')[0],
      data_period_start: "2023-01-01",
      data_period_end: "2023-03-31",
      region: region,
      practice_size: practiceSize,
      practice_type: "medspa",
      sample_size: 125,
      metrics: {
        financial: {
          avg_monthly_revenue: 87500,
          revenue_growth_rate: 14.5,
          profit_margin: 38.2,
          avg_transaction_value: 1250,
          cost_per_acquisition: 210
        },
        operational: {
          provider_utilization: 82.5,
          room_utilization: 78.3,
          avg_treatment_time: 45,
          rebook_rate: 68.9,
          no_show_rate: 3.7
        },
        marketing: {
          lead_conversion_rate: 32.5,
          customer_acquisition_cost: 210,
          website_conversion_rate: 3.2,
          social_engagement_rate: 4.8,
          referral_rate: 21.5
        },
        customer: {
          retention_rate: 78.4,
          lifetime_value: 8500,
          satisfaction_score: 91.2,
          avg_treatments_per_patient: 4.2,
          repeat_visit_rate: 65.8
        }
      },
      service_distribution: [
        { category: "Injectables", percentage: 36, avg_price: 650, growth_rate: 18.5 },
        { category: "Laser Treatments", percentage: 24, avg_price: 850, growth_rate: 12.2 },
        { category: "Facials", percentage: 18, avg_price: 250, growth_rate: 8.7 },
        { category: "Body Treatments", percentage: 14, avg_price: 1200, growth_rate: 22.5 },
        { category: "Other Services", percentage: 8, avg_price: 450, growth_rate: 5.2 }
      ],
      percentiles: {
        revenue: {
          p25: 65000,
          p50: 87500,
          p75: 125000,
          p90: 180000
        },
        profit_margin: {
          p25: 28,
          p50: 38,
          p75: 45,
          p90: 52
        },
        lead_conversion: {
          p25: 22,
          p50: 32,
          p75: 42,
          p90: 48
        },
        retention_rate: {
          p25: 65,
          p50: 78,
          p75: 85,
          p90: 92
        }
      },
      trends: {
        revenue_growth: 14.5,
        new_patient_growth: 9.8,
        treatment_volume_growth: 12.3,
        average_price_change: 3.5
      }
    };
  };

  // Generate business metrics (in production this would be from actual business data)
  const getBusinessMetrics = () => {
    if (!businessData) return null;
    
    // For demo purposes, generate metrics that show slight variations from benchmark
    const benchmarkFinancial = benchmarkData?.metrics?.financial || {};
    const variance = (min, max) => min + Math.random() * (max - min);
    
    return {
      financial: {
        avg_monthly_revenue: businessData.monthly_revenue || safeGetBenchmarkValue('financial.avg_monthly_revenue', 87500) * variance(0.8, 1.2),
        revenue_growth_rate: benchmarkFinancial.revenue_growth_rate * variance(0.7, 1.3),
        profit_margin: benchmarkFinancial.profit_margin * variance(0.85, 1.15),
        avg_transaction_value: benchmarkFinancial.avg_transaction_value * variance(0.9, 1.1),
        cost_per_acquisition: benchmarkFinancial.cost_per_acquisition * variance(0.9, 1.1)
      }
      // Other metrics would be included in a real implementation
    };
  };

  // Get the business's percentile rank based on revenue
  const getBusinessPercentile = () => {
    if (!businessData || !benchmarkData) return null;
    
    const revenue = businessData.monthly_revenue;
    const percentiles = benchmarkData.percentiles.revenue;
    
    if (!revenue || !percentiles) return null;
    
    if (revenue < percentiles.p25) return { rank: 'bottom', percentile: 'Below 25th' };
    if (revenue < percentiles.p50) return { rank: 'below-average', percentile: '25th to 50th' };
    if (revenue < percentiles.p75) return { rank: 'above-average', percentile: '50th to 75th' };
    if (revenue < percentiles.p90) return { rank: 'high', percentile: '75th to 90th' };
    return { rank: 'top', percentile: 'Above 90th' };
  };

  const businessPercentile = getBusinessPercentile();
  const businessMetrics = getBusinessMetrics();

  if (loading) {
    return (
      <div className="p-6 bg-gray-50 min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex justify-between mb-6">
            <h1 className="text-2xl font-bold text-gray-900">Benchmarking</h1>
          </div>
          <div className="animate-pulse">
            <div className="h-32 bg-gray-200 rounded-lg mb-6"></div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="h-72 bg-gray-200 rounded-lg"></div>
              <div className="h-72 bg-gray-200 rounded-lg"></div>
              <div className="h-72 bg-gray-200 rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Industry Benchmarking</h1>
            <p className="text-gray-500">Compare your performance against industry standards</p>
          </div>

          <div className="flex flex-wrap gap-3">
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Time Period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="30days">Last 30 Days</SelectItem>
                <SelectItem value="90days">Last 90 Days</SelectItem>
                <SelectItem value="12months">Last 12 Months</SelectItem>
              </SelectContent>
            </Select>

            <Select value={region} onValueChange={setRegion}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Region" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Regions</SelectItem>
                <SelectItem value="northeast">Northeast</SelectItem>
                <SelectItem value="southeast">Southeast</SelectItem>
                <SelectItem value="midwest">Midwest</SelectItem>
                <SelectItem value="southwest">Southwest</SelectItem>
                <SelectItem value="west">West</SelectItem>
              </SelectContent>
            </Select>

            <Select value={practiceSize} onValueChange={setPracticeSize}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Practice Size" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sizes</SelectItem>
                <SelectItem value="small">Small (&lt; $500k/yr)</SelectItem>
                <SelectItem value="medium">Medium ($500k-$1.5M/yr)</SelectItem>
                <SelectItem value="large">Large (&gt; $1.5M/yr)</SelectItem>
              </SelectContent>
            </Select>

            <Button variant="outline" className="flex gap-2">
              <Download size={16} />
              <span>Export Report</span>
            </Button>
          </div>
        </div>

        {businessPercentile && (
          <Card className="bg-gradient-to-r from-[#8890ea]/10 to-white border-[#8890ea]/20">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row justify-between items-center">
                <div>
                  <h2 className="text-xl font-bold text-gray-900">Your Business Performance</h2>
                  <p className="text-gray-600">
                    Your practice is in the <span className="font-bold text-[#8890ea]">{businessPercentile.percentile} percentile</span> compared to similar businesses
                  </p>
                </div>
                <PercentileIndicator 
                  percentileRank={businessPercentile.rank} 
                  label={businessPercentile.percentile}
                  className="mt-4 md:mt-0"
                />
              </div>
            </CardContent>
          </Card>
        )}

        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full sm:w-auto sm:inline-grid grid-cols-4 sm:grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="financial">Financial</TabsTrigger>
            <TabsTrigger value="operational">Operational</TabsTrigger>
            <TabsTrigger value="marketing">Marketing</TabsTrigger>
          </TabsList>
          
          {/* Overview Tab */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Avg. Monthly Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${safeGetBenchmarkValue('financial.avg_monthly_revenue', 0).toLocaleString()}
                  </div>
                  <BenchmarkComparison
                    benchmarkValue={safeGetBenchmarkValue('financial.avg_monthly_revenue', 0)}
                    yourValue={businessMetrics?.financial?.avg_monthly_revenue || 0}
                    format="currency"
                    higherIsBetter={true}
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Profit Margin</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {safeGetBenchmarkValue('financial.profit_margin', 0).toFixed(1)}%
                  </div>
                  <BenchmarkComparison
                    benchmarkValue={safeGetBenchmarkValue('financial.profit_margin', 0)}
                    yourValue={businessMetrics?.financial?.profit_margin || 0}
                    format="percentage"
                    higherIsBetter={true}
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Lead Conversion</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {safeGetBenchmarkValue('marketing.lead_conversion_rate', 0).toFixed(1)}%
                  </div>
                  <BenchmarkComparison
                    benchmarkValue={safeGetBenchmarkValue('marketing.lead_conversion_rate', 0)}
                    yourValue={businessMetrics?.marketing?.lead_conversion_rate || 0}
                    format="percentage"
                    higherIsBetter={true}
                  />
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-500">Patient Retention</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {safeGetBenchmarkValue('customer.retention_rate', 0).toFixed(1)}%
                  </div>
                  <BenchmarkComparison
                    benchmarkValue={safeGetBenchmarkValue('customer.retention_rate', 0)}
                    yourValue={businessMetrics?.customer?.retention_rate || 0}
                    format="percentage"
                    higherIsBetter={true}
                  />
                </CardContent>
              </Card>
            </div>

            <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Industry Growth Trends</CardTitle>
                  <CardDescription>Year-over-year changes in key metrics</CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="h-80">
                    <IndustryTrendChart trends={benchmarkData.trends} />
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader>
                  <CardTitle>Service Category Distribution</CardTitle>
                  <CardDescription>Percentage breakdown by service type</CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        layout="vertical"
                        data={benchmarkData.service_distribution}
                        margin={{
                          top: 20,
                          right: 30,
                          left: 70,
                          bottom: 5,
                        }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis type="number" domain={[0, 100]} tickFormatter={(tick) => `${tick}%`} />
                        <YAxis dataKey="category" type="category" width={70} />
                        <Tooltip 
                          formatter={(value, name) => [`${value}%`, 'Percentage']}
                          labelFormatter={(value) => `${value}`}
                        />
                        <Legend />
                        <Bar dataKey="percentage" name="Market Share" fill="#8884d8" radius={[0, 4, 4, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Industry Performance Radar</CardTitle>
                <CardDescription>How your business compares across key metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-96">
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart cx="50%" cy="50%" outerRadius="80%" data={[
                      { metric: "Revenue", benchmark: 100, yours: businessMetrics?.financial?.avg_monthly_revenue ? (businessMetrics.financial.avg_monthly_revenue / safeGetBenchmarkValue('financial.avg_monthly_revenue', 1)) * 100 : 0 },
                      { metric: "Profit %", benchmark: 100, yours: businessMetrics?.financial?.profit_margin ? (businessMetrics.financial.profit_margin / safeGetBenchmarkValue('financial.profit_margin', 1)) * 100 : 0 },
                      { metric: "Lead Conv", benchmark: 100, yours: businessMetrics?.marketing?.lead_conversion_rate ? (businessMetrics.marketing.lead_conversion_rate / safeGetBenchmarkValue('marketing.lead_conversion_rate', 1)) * 100 : 0 },
                      { metric: "Retention", benchmark: 100, yours: businessMetrics?.customer?.retention_rate ? (businessMetrics.customer.retention_rate / safeGetBenchmarkValue('customer.retention_rate', 1)) * 100 : 0 },
                      { metric: "Tx Value", benchmark: 100, yours: businessMetrics?.financial?.avg_transaction_value ? (businessMetrics.financial.avg_transaction_value / safeGetBenchmarkValue('financial.avg_transaction_value', 1)) * 100 : 0 },
                      { metric: "Growth", benchmark: 100, yours: businessMetrics?.financial?.revenue_growth_rate ? (businessMetrics.financial.revenue_growth_rate / safeGetBenchmarkValue('financial.revenue_growth_rate', 1)) * 100 : 0 },
                    ]}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="metric" />
                      <PolarRadiusAxis angle={30} domain={[0, 150]} />
                      <Radar name="Industry Benchmark" dataKey="benchmark" stroke="#8884d8" fill="#8884d8" fillOpacity={0.1} />
                      <Radar name="Your Business" dataKey="yours" stroke="#82ca9d" fill="#82ca9d" fillOpacity={0.5} />
                      <Legend />
                      <Tooltip formatter={(value) => [`${value.toFixed(0)}% of benchmark`, ""]} />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
            
            <Alert variant="outline" className="bg-amber-50 border-amber-200">
              <Info className="h-4 w-4 text-amber-600" />
              <AlertTitle className="text-amber-800">Benchmark Information</AlertTitle>
              <AlertDescription className="text-amber-700">
                Data based on {benchmarkData.sample_size} MedSpa businesses. Last updated: {new Date(benchmarkData.report_date).toLocaleDateString()}.
              </AlertDescription>
            </Alert>
          </TabsContent>

          {/* Financial Tab */}
          <TabsContent value="financial">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Financial Metrics Comparison</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-8">
                    <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-5">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Monthly Revenue</h3>
                        <p className="text-2xl font-bold">${safeGetBenchmarkValue('financial.avg_monthly_revenue', 0).toLocaleString()}</p>
                        <BenchmarkComparison
                          benchmarkValue={safeGetBenchmarkValue('financial.avg_monthly_revenue', 0)}
                          yourValue={businessMetrics?.financial?.avg_monthly_revenue || 0}
                          format="currency"
                        />
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Revenue Growth</h3>
                        <p className="text-2xl font-bold">{safeGetBenchmarkValue('financial.revenue_growth_rate', 0).toFixed(1)}%</p>
                        <BenchmarkComparison
                          benchmarkValue={safeGetBenchmarkValue('financial.revenue_growth_rate', 0)}
                          yourValue={businessMetrics?.financial?.revenue_growth_rate || 0}
                          format="percentage"
                        />
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Profit Margin</h3>
                        <p className="text-2xl font-bold">{benchmarkData.metrics.financial.profit_margin.toFixed(1)}%</p>
                        <BenchmarkComparison 
                          benchmarkValue={benchmarkData.metrics.financial.profit_margin}
                          yourValue={businessMetrics?.financial?.profit_margin || 0}
                          format="percentage"
                        />
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Avg Transaction</h3>
                        <p className="text-2xl font-bold">${benchmarkData.metrics.financial.avg_transaction_value.toLocaleString()}</p>
                        <BenchmarkComparison 
                          benchmarkValue={benchmarkData.metrics.financial.avg_transaction_value}
                          yourValue={businessMetrics?.financial?.avg_transaction_value || 0}
                          format="currency"
                        />
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Cost Per Acquisition</h3>
                        <p className="text-2xl font-bold">${benchmarkData.metrics.financial.cost_per_acquisition.toLocaleString()}</p>
                        <BenchmarkComparison 
                          benchmarkValue={benchmarkData.metrics.financial.cost_per_acquisition}
                          yourValue={businessMetrics?.financial?.cost_per_acquisition || 0}
                          format="currency"
                          higherIsBetter={false}
                        />
                      </div>
                    </div>
                    
                    {/* Additional financial charts would go here */}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Operational Tab */}
          <TabsContent value="operational">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Operational Efficiency</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-8">
                    <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-5">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Provider Utilization</h3>
                        <p className="text-2xl font-bold">{benchmarkData.metrics.operational.provider_utilization.toFixed(1)}%</p>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Room Utilization</h3>
                        <p className="text-2xl font-bold">{benchmarkData.metrics.operational.room_utilization.toFixed(1)}%</p>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Avg Treatment Time</h3>
                        <p className="text-2xl font-bold">{benchmarkData.metrics.operational.avg_treatment_time} mins</p>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Rebook Rate</h3>
                        <p className="text-2xl font-bold">{benchmarkData.metrics.operational.rebook_rate.toFixed(1)}%</p>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">No Show Rate</h3>
                        <p className="text-2xl font-bold">{benchmarkData.metrics.operational.no_show_rate.toFixed(1)}%</p>
                      </div>
                    </div>
                    
                    {/* Additional operational charts would go here */}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Marketing Tab */}
          <TabsContent value="marketing">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Marketing Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-8">
                    <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-5">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Lead Conversion</h3>
                        <p className="text-2xl font-bold">{benchmarkData.metrics.marketing.lead_conversion_rate.toFixed(1)}%</p>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Customer Acq. Cost</h3>
                        <p className="text-2xl font-bold">${benchmarkData.metrics.marketing.customer_acquisition_cost}</p>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Website Conversion</h3>
                        <p className="text-2xl font-bold">{benchmarkData.metrics.marketing.website_conversion_rate.toFixed(1)}%</p>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Social Engagement</h3>
                        <p className="text-2xl font-bold">{benchmarkData.metrics.marketing.social_engagement_rate.toFixed(1)}%</p>
                      </div>
                      
                      <div>
                        <h3 className="text-sm font-medium text-gray-500 mb-1">Referral Rate</h3>
                        <p className="text-2xl font-bold">{benchmarkData.metrics.marketing.referral_rate.toFixed(1)}%</p>
                      </div>
                    </div>
                    
                    {/* Additional marketing charts would go here */}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
