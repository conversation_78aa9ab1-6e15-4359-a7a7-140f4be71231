import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ExternalLink, Linkedin, Twitter, Instagram, Youtube, Facebook, ChevronLeft, GraduationCap, Award } from 'lucide-react';
import { Educator } from '@/api/entities';
import { Course } from '@/api/entities';
import { safeParseJSONField } from '@/utils/dataParser';

export default function EducatorProfile() {
  const [educator, setEducator] = useState(null);
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    const urlParams = new URLSearchParams(window.location.search);
    const id = urlParams.get('id');
    
    if (!id) {
      window.location.href = '/Educators';
      return;
    }

    try {
      setLoading(true);
      const [educatorData, courseData] = await Promise.all([
        Educator.get(id),
        Course.list()
      ]);
      
      if (!educatorData) {
        window.location.href = '/Educators';
        return;
      }
      
      setEducator(educatorData);
      
      // Filter courses by this educator
      const educatorCourses = courseData.filter(
        course => course.educator && course.educator.id === educatorData.id
      );
      
      setCourses(educatorCourses);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const socialIcons = {
    linkedin: { icon: Linkedin, color: "text-[#0077B5]" },
    twitter: { icon: Twitter, color: "text-[#1DA1F2]" },
    facebook: { icon: Facebook, color: "text-[#4267B2]" },
    youtube: { icon: Youtube, color: "text-[#FF0000]" },
    instagram: { icon: Instagram, color: "text-[#E1306C]" }
  };

  if (loading || !educator) {
    return (
      <div className="p-6 flex justify-center items-center min-h-screen">
        <div className="text-center">
          <GraduationCap className="w-10 h-10 text-[#8890ea] mb-4 mx-auto animate-pulse" />
          <p>Loading educator profile...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto space-y-6">
        <Button 
          variant="ghost" 
          className="flex items-center gap-1"
          onClick={() => window.history.back()}
        >
          <ChevronLeft className="w-4 h-4" />
          Back
        </Button>
        
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="md:flex">
            <div className="md:w-1/3 p-6">
              <div className="relative w-48 h-48 mx-auto rounded-full overflow-hidden">
                <img 
                  src={educator.headshot_url} 
                  alt={educator.full_name} 
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    e.target.src = 'https://placehold.co/400x400?text=Educator';
                  }}
                />
              </div>
              
              <div className="mt-6 text-center md:text-left">
                <h1 className="text-2xl font-bold">{educator.full_name}</h1>
                <p className="text-gray-600">{educator.title}</p>
                
                {educator.company && (
                  <p className="text-gray-600 flex items-center justify-center md:justify-start gap-1 mt-1">
                    {educator.company}
                    {educator.company_website && (
                      <a 
                        href={educator.company_website} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <ExternalLink className="h-3.5 w-3.5" />
                      </a>
                    )}
                  </p>
                )}
                
                <div className="flex justify-center md:justify-start mt-4 space-x-3">
                  {educator.social_media && Object.entries(educator.social_media).map(([platform, url]) => {
                    if (!url) return null;
                    const { icon: Icon, color } = socialIcons[platform] || {};
                    if (!Icon) return null;
                    
                    return (
                      <a 
                        key={platform}
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`${color} hover:opacity-80 transition-opacity`}
                      >
                        <Icon className="w-5 h-5" />
                      </a>
                    );
                  })}
                </div>
                
                {(() => {
                  const expertise = safeParseJSONField(educator.expertise, []);
                  return expertise && expertise.length > 0 && (
                    <div className="mt-6">
                      <h3 className="text-sm font-medium mb-2">Areas of Expertise</h3>
                      <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                        {expertise.map((area, index) => (
                          <Badge key={index} variant="outline">
                            {area}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  );
                })()}
                
                {educator.credentials && educator.credentials.length > 0 && (
                  <div className="mt-6">
                    <h3 className="text-sm font-medium mb-2">Credentials</h3>
                    <div className="flex flex-wrap gap-2 justify-center md:justify-start">
                      {educator.credentials.map((credential, index) => (
                        <Badge key={index} variant="secondary">
                          {credential}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <div className="md:w-2/3 p-6 border-t md:border-t-0 md:border-l">
              <h2 className="text-xl font-bold mb-4">Biography</h2>
              <div className="prose max-w-none">
                <p className="whitespace-pre-line">{educator.bio}</p>
              </div>
              
              {courses.length > 0 && (
                <div className="mt-8">
                  <h2 className="text-xl font-bold mb-4 flex items-center gap-2">
                    <GraduationCap className="w-5 h-5 text-[#8890ea]" />
                    Courses by {educator.full_name.split(' ')[0]}
                  </h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {courses.map(course => (
                      <Card key={course.id} className="overflow-hidden flex">
                        <div className="w-1/3">
                          <img 
                            src={course.thumbnail_url} 
                            alt={course.title}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.target.src = 'https://placehold.co/640x360?text=Course';
                            }}
                          />
                        </div>
                        <div className="w-2/3 p-4">
                          <h3 className="font-bold line-clamp-2">{course.title}</h3>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline">{course.difficulty}</Badge>
                            {course.topics && course.topics[0] && (
                              <Badge variant="outline">{course.topics[0]}</Badge>
                            )}
                          </div>
                          <Button 
                            className="mt-3 w-full bg-[#8890ea] hover:bg-[#7a7dd4] text-white"
                            onClick={() => window.location.href = `/CourseDetail?id=${course.id}`}
                          >
                            View Course
                          </Button>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
