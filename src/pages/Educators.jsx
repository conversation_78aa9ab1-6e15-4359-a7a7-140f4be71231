import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON> } from '@/api/entities';
import { Course } from '@/api/entities';
import { CourseProgress } from '@/api/entities';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Search, Filter, Book, Award, User, UserCheck } from "lucide-react";
import { createPageUrl } from '@/utils';
import EducatorCard from '@/components/educators/EducatorCard';
import { safeParseJSONField } from '@/utils/dataParser';

export default function Educators() {
  const [educators, setEducators] = useState([]);
  const [filteredEducators, setFilteredEducators] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadEducators();
  }, []);

  useEffect(() => {
    filterEducators();
  }, [searchTerm, filter, educators]);

  const loadEducators = async () => {
    try {
      setLoading(true);
      const data = await Educator.list();
      setEducators(data || []);
      setFilteredEducators(data || []);
    } catch (error) {
      console.error('Error loading educators:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterEducators = () => {
    let results = [...educators];

    // Apply search term filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      results = results.filter(educator =>
        educator.full_name?.toLowerCase().includes(term) ||
        educator.title?.toLowerCase().includes(term) ||
        educator.company?.toLowerCase().includes(term) ||
        educator.bio?.toLowerCase().includes(term)
      );
    }

    // Apply category filter
    if (filter !== 'all') {
      results = results.filter(educator => {
        if (filter === 'featured') {
          return educator.featured;
        }

        // Parse expertise safely for filtering
        const expertise = safeParseJSONField(educator.expertise, []);
        return expertise.includes(filter);
      });
    }

    setFilteredEducators(results);
  };

  const getEducatorInitials = (name) => {
    if (!name) return 'ED';
    return name.split(' ').slice(0, 2).map(n => n[0]).join('');
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Educators</h1>
          <p className="text-gray-500">Learn from industry leading experts and thought leaders</p>
        </div>
        
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
          <div className="relative w-full md:w-80">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search educators..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          <Tabs defaultValue="all" onValueChange={setFilter} className="w-full md:w-auto">
            <TabsList>
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="marketing">Marketing</TabsTrigger>
              <TabsTrigger value="business">Business</TabsTrigger>
              <TabsTrigger value="clinical">Clinical</TabsTrigger>
              <TabsTrigger value="featured">Featured</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6 animate-pulse">
            {[1, 2, 3, 4, 5, 6].map(i => (
              <div key={i} className="bg-white h-64 rounded-lg shadow"></div>
            ))}
          </div>
        ) : filteredEducators.length === 0 ? (
          <div className="text-center py-12">
            <User className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-lg font-medium text-gray-900">No educators found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search or filter to find what you're looking for.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
            {filteredEducators.map((educator) => (
              <EducatorCard 
                key={educator.id} 
                educator={educator} 
                onClick={() => window.location.href = createPageUrl(`EducatorProfile?id=${educator.id}`)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
