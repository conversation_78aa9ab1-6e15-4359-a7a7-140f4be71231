import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ReferenceLine
} from 'recharts';
import {
  LineChart as LineChartIcon,
  TrendingUp,
  CalendarRange,
  SlidersHorizontal,
  Download,
  Share2,
  Info,
  AlertCircle,
  ChevronDown,
  Save
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import { ForecastModel } from '@/api/entities';
import { parseForecastModelData, safeGet } from '@/utils/dataParser';

export default function IndustryForecast() {
  const [loading, setLoading] = useState(true);
  const [forecasts, setForecasts] = useState([]);
  const [currentForecast, setCurrentForecast] = useState(null);
  const [forecastPeriod, setForecastPeriod] = useState('90_days');
  const [forecastType, setForecastType] = useState('revenue');
  const [showScenarioBuilder, setShowScenarioBuilder] = useState(false);
  const [scenarioDialog, setScenarioDialog] = useState(false);
  const [scenarioName, setScenarioName] = useState('');
  
  // Scenario parameters
  const [scenarioParams, setScenarioParams] = useState({
    marketingIncrease: 0,
    priceIncrease: 0,
    newTreatments: false,
    staffExpansion: 0
  });

  useEffect(() => {
    loadRealForecastData();
  }, [forecastPeriod, forecastType]);

  const loadForecasts = async () => {
    setLoading(true);
    try {
      // In a production environment, we would load real forecast data
      // For now, we'll use demo data
      
      const baseForecast = {
        model_name: "Standard Forecast",
        model_type: forecastType,
        created_date: new Date().toISOString(),
        forecast_period: forecastPeriod,
        accuracy_score: 0.86,
        confidence_interval: 0.9,
        influencing_factors: [
          { factor: "Seasonal Trends", impact_weight: 0.25, direction: "positive" },
          { factor: "Marketing Spend", impact_weight: 0.3, direction: "positive" },
          { factor: "Market Competition", impact_weight: 0.15, direction: "negative" },
          { factor: "Economic Indicators", impact_weight: 0.2, direction: "neutral" },
          { factor: "Treatment Popularity", impact_weight: 0.1, direction: "positive" }
        ],
      };
      
      // Generate prediction data based on forecast period
      let predictions = [];
      let startDate = new Date();
      let months = forecastPeriod === '30_days' ? 1 : 
                  forecastPeriod === '90_days' ? 3 : 
                  forecastPeriod === '6_months' ? 6 : 12;
                  
      // Base values for different forecast types
      const baseValue = forecastType === 'revenue' ? 78000 :
                       forecastType === 'patient_volume' ? 120 :
                       forecastType === 'lead_conversion' ? 32 : 145;
      
      // Generate monthly data points
      for (let i = 0; i < months; i++) {
        let forecastDate = new Date(startDate);
        forecastDate.setMonth(startDate.getMonth() + i);
        
        // Add some randomness and growth trend
        const randomFactor = 0.95 + Math.random() * 0.1; // 0.95 to 1.05
        const growthFactor = 1 + (i * 0.02); // 2% growth per month
        
        const value = Math.round(baseValue * randomFactor * growthFactor);
        const uncertainty = baseValue * 0.1 * (i/5 + 1); // Uncertainty increases over time
        
        predictions.push({
          date: forecastDate.toISOString().split('T')[0],
          value: value,
          lower_bound: Math.round(value - uncertainty),
          upper_bound: Math.round(value + uncertainty)
        });
      }
      
      const forecast = {
        ...baseForecast,
        predictions: predictions
      };
      
      setForecasts([forecast]);
      setCurrentForecast(forecast);
      
    } catch (error) {
      console.error('Error loading forecast data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  const generateScenario = () => {
    if (!currentForecast) return;
    
    // Create a modified copy of the current forecast
    const basePredictions = currentForecast.predictions;
    let scenarioPredictions = [];
    
    // Apply scenario parameters to the forecast
    const marketingImpact = scenarioParams.marketingIncrease * 0.03; // 3% increase per 100% marketing increase
    const priceImpact = scenarioParams.priceIncrease * 0.01; // 1% increase per 1% price increase
    const treatmentImpact = scenarioParams.newTreatments ? 0.08 : 0; // 8% increase for new treatments
    const staffImpact = scenarioParams.staffExpansion * 0.05; // 5% increase per staff member
    
    // Total growth factor from all parameters
    const totalImpact = 1 + marketingImpact + priceImpact + treatmentImpact + staffImpact;
    
    // Apply to all prediction points
    basePredictions.forEach(pred => {
      scenarioPredictions.push({
        ...pred,
        value: Math.round(pred.value * totalImpact),
        lower_bound: Math.round(pred.lower_bound * totalImpact),
        upper_bound: Math.round(pred.upper_bound * totalImpact)
      });
    });
    
    // Create the scenario forecast
    const scenarioForecast = {
      ...currentForecast,
      model_name: scenarioName || "Custom Scenario",
      predictions: scenarioPredictions,
      scenario_assumptions: {
        marketing_increase: scenarioParams.marketingIncrease,
        price_increase: scenarioParams.priceIncrease,
        new_treatments: scenarioParams.newTreatments,
        staff_expansion: scenarioParams.staffExpansion
      }
    };
    
    // Add to forecasts list
    setForecasts([...forecasts, scenarioForecast]);
    setCurrentForecast(scenarioForecast);
    setScenarioDialog(false);
    setShowScenarioBuilder(false);
  };

  const getYAxisLabel = () => {
    switch(forecastType) {
      case 'revenue': return 'Revenue ($)';
      case 'patient_volume': return 'Patient Count';
      case 'lead_conversion': return 'Conversion Rate (%)';
      case 'treatment_demand': return 'Treatments';
      default: return '';
    }
  };

  // Load real forecast data from the API
  const loadRealForecastData = async () => {
    try {
      setLoading(true);
      const accountId = sessionStorage.getItem('accountId') || 'medspa_acct_main_demo';

      // Get forecast models for the account
      const rawForecasts = await ForecastModel.filter({
        account_id: accountId,
        model_type: forecastType
      });

      if (rawForecasts.length > 0) {
        // Parse JSON string fields in forecast data
        const parsedForecasts = rawForecasts.map(forecast => parseForecastModelData(forecast));
        setForecasts(parsedForecasts);

        // Set the most recent forecast as current
        const mostRecent = parsedForecasts.sort((a, b) =>
          new Date(b.created_date) - new Date(a.created_date)
        )[0];
        setCurrentForecast(mostRecent);
      } else {
        // Fallback to generated forecast if no real data
        generateForecast();
      }
    } catch (error) {
      console.error('Error loading forecast data:', error);
      // Fallback to generated forecast
      generateForecast();
    } finally {
      setLoading(false);
    }
  };

  // Helper functions for safe predictions access
  const getLastPrediction = () => {
    const predictions = safeGet(currentForecast, 'predictions', []);
    return predictions.length > 0 ? predictions[predictions.length - 1] : { value: 0, lower_bound: 0, upper_bound: 0 };
  };

  const getFirstPrediction = () => {
    const predictions = safeGet(currentForecast, 'predictions', []);
    return predictions.length > 0 ? predictions[0] : { value: 0, lower_bound: 0, upper_bound: 0 };
  };

  const getPredictionsAverage = () => {
    const predictions = safeGet(currentForecast, 'predictions', []);
    if (predictions.length === 0) return 0;
    return predictions.reduce((sum, p) => sum + (p.value || 0), 0) / predictions.length;
  };

  const formatYAxis = (value) => {
    if (forecastType === 'revenue') {
      return value >= 1000 ? `$${value/1000}K` : `$${value}`;
    }
    if (forecastType === 'lead_conversion') {
      return `${value}%`;
    }
    return value;
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Industry Forecasting</h1>
            <p className="text-gray-500">Predict future performance and explore growth scenarios</p>
          </div>
          <div className="flex flex-wrap gap-3">
            <Button 
              variant="outline" 
              className="flex items-center gap-2"
              onClick={() => setScenarioDialog(true)}
            >
              <SlidersHorizontal className="h-4 w-4" />
              Build Scenario
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Export
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="md:col-span-3">
            <Card className="h-full bg-white shadow-sm">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>
                    {forecastType === 'revenue' ? 'Revenue Forecast' : 
                     forecastType === 'patient_volume' ? 'Patient Volume Forecast' :
                     forecastType === 'lead_conversion' ? 'Lead Conversion Forecast' :
                     'Treatment Demand Forecast'}
                  </CardTitle>
                  <Select value={forecastPeriod} onValueChange={setForecastPeriod}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Forecast Period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30_days">30 Days</SelectItem>
                      <SelectItem value="90_days">90 Days</SelectItem>
                      <SelectItem value="6_months">6 Months</SelectItem>
                      <SelectItem value="12_months">12 Months</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <CardDescription>
                  Based on historical trends and market analysis with {currentForecast?.confidence_interval * 100 || 90}% confidence interval
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  {loading ? (
                    <div className="animate-pulse h-full bg-gray-100 rounded"></div>
                  ) : (
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart
                        data={safeGet(currentForecast, 'predictions', [])}
                        margin={{ top: 10, right: 30, left: 30, bottom: 30 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" vertical={false} />
                        <XAxis 
                          dataKey="date" 
                          tickFormatter={(date) => {
                            const d = new Date(date);
                            return `${d.getMonth() + 1}/${d.getFullYear().toString().substr(-2)}`;
                          }}
                        />
                        <YAxis 
                          domain={[
                            dataMin => Math.max(0, Math.floor(dataMin * 0.9)), 
                            dataMax => Math.ceil(dataMax * 1.1)
                          ]} 
                          tickFormatter={formatYAxis}
                          label={{ 
                            value: getYAxisLabel(), 
                            angle: -90, 
                            position: 'insideLeft',
                            style: { textAnchor: 'middle' } 
                          }}
                        />
                        <Tooltip 
                          formatter={(value, name) => {
                            if (name === 'value' && forecastType === 'revenue') {
                              return [`$${value.toLocaleString()}`, 'Forecast'];
                            }
                            if (name === 'value' && forecastType === 'lead_conversion') {
                              return [`${value}%`, 'Forecast'];
                            }
                            return [value, name === 'value' ? 'Forecast' : name];
                          }}
                          labelFormatter={(label) => {
                            const date = new Date(label);
                            return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
                          }}
                        />
                        <Area 
                          type="monotone" 
                          dataKey="upper_bound" 
                          stroke="none" 
                          fill="#8890ea" 
                          fillOpacity={0.1} 
                          activeDot={false}
                        />
                        <Area 
                          type="monotone" 
                          dataKey="lower_bound" 
                          stroke="none" 
                          fill="#8890ea" 
                          fillOpacity={0.1} 
                          activeDot={false}
                        />
                        <Line 
                          type="monotone" 
                          dataKey="value" 
                          stroke="#8890ea" 
                          strokeWidth={3}
                          dot={{ stroke: '#8890ea', strokeWidth: 2, r: 4, fill: 'white' }}
                          activeDot={{ r: 6, strokeWidth: 2 }}
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  )}
                </div>
                
                <div className="border-t mt-4 pt-4">
                  <div className="flex flex-wrap gap-2 text-sm text-gray-600">
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-sm bg-[#8890ea] mr-1"></div>
                      <span>Forecast</span>
                    </div>
                    <div className="flex items-center">
                      <div className="w-3 h-3 rounded-sm bg-[#8890ea]/20 mr-1"></div>
                      <span>Confidence Interval</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="text-base">Forecast Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div>
                    <Label htmlFor="forecast-type">Select metric to forecast</Label>
                    <Select value={forecastType} onValueChange={setForecastType}>
                      <SelectTrigger id="forecast-type">
                        <SelectValue placeholder="Forecast Type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="revenue">Revenue</SelectItem>
                        <SelectItem value="patient_volume">Patient Volume</SelectItem>
                        <SelectItem value="lead_conversion">Lead Conversion</SelectItem>
                        <SelectItem value="treatment_demand">Treatment Demand</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                {currentForecast && (
                  <div className="mt-6 pt-4 border-t space-y-4">
                    <div>
                      <div className="flex justify-between text-sm">
                        <span>Model Accuracy</span>
                        <span>{Math.round(currentForecast.accuracy_score * 100)}%</span>
                      </div>
                      <Progress 
                        value={currentForecast.accuracy_score * 100} 
                        className="h-2 mt-1" 
                      />
                    </div>
                    
                    <div className="text-sm">
                      <p className="font-medium mb-2">Key Influencing Factors:</p>
                      <ul className="space-y-1">
                        {safeGet(currentForecast, 'influencing_factors', []).map((factor, index) => (
                          <li key={index} className="flex items-center justify-between">
                            <span>{factor.factor}</span>
                            <span className={
                              factor.direction === 'positive' ? 'text-green-600' :
                              factor.direction === 'negative' ? 'text-red-600' :
                              'text-gray-600'
                            }>
                              {factor.direction === 'positive' ? '↑' :
                               factor.direction === 'negative' ? '↓' : '–'}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="text-base">Available Forecasts</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {forecasts.map((forecast, index) => (
                    <div 
                      key={index} 
                      className={`p-3 rounded-md border cursor-pointer transition-colors ${
                        currentForecast === forecast ? 'bg-[#8890ea]/10 border-[#8890ea]/20' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => setCurrentForecast(forecast)}
                    >
                      <div className="font-medium">{forecast.model_name}</div>
                      <div className="text-xs text-gray-500 mt-1">
                        {safeGet(forecast, 'scenario_assumptions') ? 'Custom Scenario' : 'Base Forecast'} •
                        {forecast.forecast_period === '30_days' ? ' 30 Days' :
                         forecast.forecast_period === '90_days' ? ' 90 Days' :
                         forecast.forecast_period === '6_months' ? ' 6 Months' : ' 12 Months'}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {safeGet(currentForecast, 'scenario_assumptions') && (
          <Card className="bg-white shadow-sm border-[#8890ea]/20">
            <CardHeader className="pb-2">
              <CardTitle>Scenario Assumptions</CardTitle>
              <CardDescription>
                Custom parameters applied to this forecast scenario
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <div className="text-sm text-gray-500">Marketing Budget</div>
                  <div className="font-medium">
                    +{safeGet(currentForecast, 'scenario_assumptions.marketing_increase', 0)}%
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Price Increase</div>
                  <div className="font-medium">
                    +{safeGet(currentForecast, 'scenario_assumptions.price_increase', 0)}%
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">New Treatments</div>
                  <div className="font-medium">
                    {safeGet(currentForecast, 'scenario_assumptions.new_treatments', false) ? 'Yes' : 'No'}
                  </div>
                </div>
                <div>
                  <div className="text-sm text-gray-500">Staff Expansion</div>
                  <div className="font-medium">
                    +{safeGet(currentForecast, 'scenario_assumptions.staff_expansion', 0)} members
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
        
        {!loading && currentForecast && (
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle>Forecast Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                  <div className="text-sm text-gray-500 mb-1">End of Period Forecast</div>
                  <div className="text-2xl font-bold">
                    {forecastType === 'revenue'
                      ? `$${getLastPrediction().value.toLocaleString()}`
                      : forecastType === 'lead_conversion'
                      ? `${getLastPrediction().value}%`
                      : getLastPrediction().value.toLocaleString()
                    }
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    Range: {forecastType === 'revenue'
                      ? `$${getLastPrediction().lower_bound.toLocaleString()} - $${getLastPrediction().upper_bound.toLocaleString()}`
                      : forecastType === 'lead_conversion'
                      ? `${getLastPrediction().lower_bound}% - ${getLastPrediction().upper_bound}%`
                      : `${getLastPrediction().lower_bound.toLocaleString()} - ${getLastPrediction().upper_bound.toLocaleString()}`
                    }
                  </div>
                </div>
                
                <div>
                  <div className="text-sm text-gray-500 mb-1">Growth Over Period</div>
                  <div className="text-2xl font-bold text-green-600">
                    +{Math.round((getLastPrediction().value / getFirstPrediction().value - 1) * 100)}%
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    From {forecastType === 'revenue'
                      ? `$${getFirstPrediction().value.toLocaleString()}`
                      : forecastType === 'lead_conversion'
                      ? `${getFirstPrediction().value}%`
                      : getFirstPrediction().value.toLocaleString()
                    } at start of period
                  </div>
                </div>
                
                <div>
                  <div className="text-sm text-gray-500 mb-1">Average Over Period</div>
                  <div className="text-2xl font-bold">
                    {forecastType === 'revenue'
                      ? `$${Math.round(getPredictionsAverage()).toLocaleString()}`
                      : forecastType === 'lead_conversion'
                      ? `${Math.round(getPredictionsAverage() * 10) / 10}%`
                      : Math.round(getPredictionsAverage()).toLocaleString()
                    }
                  </div>
                  <div className="text-sm text-gray-500 mt-1">
                    Confidence Score: {Math.round(currentForecast.accuracy_score * 100)}%
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="bg-gray-50 border-t text-sm text-gray-500">
              <div className="flex items-start gap-2">
                <Info className="h-4 w-4 mt-0.5 flex-shrink-0" />
                <p>
                  This forecast is based on historical data, industry trends, and market analysis. 
                  Actual results may vary. The confidence interval represents the range of potential outcomes.
                </p>
              </div>
            </CardFooter>
          </Card>
        )}
      </div>
      
      {/* Scenario Dialog */}
      <Dialog open={scenarioDialog} onOpenChange={setScenarioDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Create Forecast Scenario</DialogTitle>
            <DialogDescription>
              Adjust parameters to create a custom forecast scenario.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="scenario-name">Scenario Name</Label>
              <Input
                id="scenario-name"
                placeholder="e.g., Growth Strategy 2024"
                value={scenarioName}
                onChange={(e) => setScenarioName(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>Marketing Budget Increase</Label>
              <div className="flex items-center gap-4">
                <Slider
                  value={[scenarioParams.marketingIncrease]}
                  onValueChange={(value) => setScenarioParams({...scenarioParams, marketingIncrease: value[0]})}
                  min={0}
                  max={100}
                  step={5}
                  className="flex-1"
                />
                <span className="w-12 text-right">{scenarioParams.marketingIncrease}%</span>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label>Price Increase</Label>
              <div className="flex items-center gap-4">
                <Slider
                  value={[scenarioParams.priceIncrease]}
                  onValueChange={(value) => setScenarioParams({...scenarioParams, priceIncrease: value[0]})}
                  min={0}
                  max={20}
                  step={1}
                  className="flex-1"
                />
                <span className="w-12 text-right">{scenarioParams.priceIncrease}%</span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch 
                id="new-treatments" 
                checked={scenarioParams.newTreatments}
                onCheckedChange={(checked) => setScenarioParams({...scenarioParams, newTreatments: checked})}
              />
              <Label htmlFor="new-treatments">Add New Treatment Services</Label>
            </div>
            
            <div className="space-y-2">
              <Label>Staff Expansion</Label>
              <div className="flex items-center gap-4">
                <Slider
                  value={[scenarioParams.staffExpansion]}
                  onValueChange={(value) => setScenarioParams({...scenarioParams, staffExpansion: value[0]})}
                  min={0}
                  max={10}
                  step={1}
                  className="flex-1"
                />
                <span className="w-12 text-right">+{scenarioParams.staffExpansion}</span>
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setScenarioDialog(false)}>
              Cancel
            </Button>
            <Button onClick={generateScenario} className="bg-[#8890ea]">
              Generate Forecast
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
