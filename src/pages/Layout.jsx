
import React from 'react';
import '@/styles/global.css';
import { Link, useLocation } from 'react-router-dom';
import { createPageUrl } from '@/utils';
import {
  LayoutDashboard,
  Globe,
  Menu,
  X,
  Target,
  TrendingUp,
  BarChart3,
  Settings,
  ChevronUp,
  ChevronDown,
  User,
  LogOut,
  PieChart,
  Share2,
  Megaphone,
  Handshake,
  Star,
  Mail,
  Users,
  Calendar,
  PhoneCall,
  ScrollText,
  BadgeDollarSign,
  Wallet,
  LineChart,
  Package,
  ClipboardList,
  UserCog,
  RefreshCw,
  FileText,
  GraduationCap,
  Building,
  ChevronsUpDown,
  Shield,
  BookOpen,
  Layout as LayoutIcon,
  HeartHandshake,
  TicketCheck,
  HelpCircle,
  Sliders,
  CreditCard,
  MessageSquare,
  Activity,
  Search,
  Briefcase,
  BarChart2,
  Map,
  Compass,
  Route,
  Building2,
  ChevronLeft,
  ChevronRight,
  FilePlus,
  Award,
  Home,
  DraftingCompass,
  Tags // Added Tags icon
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from '@/lib/utils';
import { User as UserEntity } from '@/api/entities';
import { MedSpaAccount } from '@/api/entities';
import { EnterpriseAccount } from '@/api/entities';
import { EnterpriseUser } from '@/api/entities';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,

} from "@/components/ui/command";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import QuantiChatBubble from '@/components/dashboard/QuantiChatBubble';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { OnboardingStatus } from '@/api/entities';
import { Toaster } from "@/components/ui/toaster";

export default function Layout({ children, currentPageName }) {
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = React.useState(false);
  const [settingsOpen, setSettingsOpen] = React.useState(false);
  const [expandedSection, setExpandedSection] = React.useState(null);
  const [user, setUser] = React.useState(null);
  const [accountSwitcherOpen, setAccountSwitcherOpen] = React.useState(false);
  const [currentAccount, setCurrentAccount] = React.useState(null);
  const [viewMode, setViewMode] = React.useState('business');
  const [accounts, setAccounts] = React.useState([]);
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);
  const [isLoadingUser, setIsLoadingUser] = React.useState(true);
  const [filteredNavigation, setFilteredNavigation] = React.useState(null);
  const [showOnboardingWizard, setShowOnboardingWizard] = React.useState(false);
  const [activeTour, setActiveTour] = React.useState({ section: null, message: '', pageToNavigate: null });
  const [showWelcomeVideo, setShowWelcomeVideo] = React.useState(false);
  const [onboardingStatusData, setOnboardingStatusData] = React.useState(null);

  const isEnterpriseAdmin = user?.is_enterprise_admin === true;

  const enterpriseNavigation = [
    {
      name: 'Enterprise Dashboard',
      icon: Building2,
      href: createPageUrl('EnterpriseDashboard')
    },
    {
      name: 'Sub-accounts',
      icon: Building,
      href: createPageUrl('EnterpriseAccounts')
    },
    {
      name: 'Users',
      icon: Users,
      href: createPageUrl('EnterpriseUsers')
    },
    {
      name: 'Learning Management',
      icon: GraduationCap,
      sections: [
        { name: 'Courses', href: createPageUrl('EnterpriseCourses'), icon: BookOpen },
        { name: 'Analytics', href: createPageUrl('EnterpriseLearningAnalytics'), icon: BarChart3 }
      ]
    },
    {
      name: 'Settings',
      icon: Settings,
      href: createPageUrl('EnterpriseSettings')
    }
  ];

  const settingsNavigation = [
    { name: "Account Settings", href: createPageUrl('AccountSettings'), icon: Settings },
    { name: "Integrations", href: createPageUrl('Integrations'), icon: RefreshCw }
  ];

  const businessNavigation = [
    ...(showOnboardingWizard ? [{ name: 'Onboarding Wizard', icon: Award, href: createPageUrl('OnboardingWizard'), highlight: true }] : []),
    {
      name: 'Dashboard',
      icon: LayoutDashboard,
      href: createPageUrl('Dashboard')
    },
    {
      name: 'My Business',
      icon: Building2,
      sections: [
        { name: 'Benchmarking', href: createPageUrl('Benchmarking'), icon: BarChart2 },
        { name: 'Business Roadmap', href: createPageUrl('BusinessRoadmap'), icon: Route },
        { name: 'Goals', href: createPageUrl('BusinessGoals'), icon: Target }
      ]
    },
    {
      name: 'Marketing',
      icon: Target,
      sections: [
        { name: 'Overview', href: createPageUrl('Marketing'), icon: PieChart },
        { name: 'Website', href: createPageUrl('MarketingWebsite'), icon: Globe },
        { name: 'Keyword Tracking', href: createPageUrl('MarketingKeywords'), icon: Search },
        { name: 'Social Media', href: createPageUrl('MarketingSocial'), icon: Share2 },
        { name: 'Advertising', href: createPageUrl('MarketingAds'), icon: Megaphone },
        { name: 'Google Business', href: createPageUrl('MarketingGoogleBusiness'), icon: Star },
      ]
    },
    {
      name: 'Sales',
      icon: TrendingUp,
      href: createPageUrl('Sales')
    },
    {
      name: 'Operations',
      icon: BarChart3,
      sections: [
        { name: 'Overview', href: createPageUrl('ServiceOverview'), icon: PieChart },
        { name: 'Revenue', href: createPageUrl('ServiceRevenue'), icon: Wallet },
        { name: 'Expenses', href: createPageUrl('ServiceExpenses'), icon: BadgeDollarSign },
        { name: 'Staff Performance', href: createPageUrl('ServiceStaffPerformance'), icon: UserCog },
      ]
    },
    {
      name: 'Reporting',
      icon: FileText,
      sections: [
        { name: 'Scorecard Reports', href: createPageUrl('Reporting'), icon: BarChart3 },
        { name: 'Report Builder', href: createPageUrl('ReportBuilder'), icon: DraftingCompass },
        { name: 'Daily Close Reports', href: createPageUrl('DailyCloseReports'), icon: ClipboardList }
      ]
    },
    {
      name: 'Learning',
      icon: GraduationCap,
      sections: [
        { name: 'Courses', href: createPageUrl('Learning'), icon: BookOpen },
        { name: 'Educators', href: createPageUrl('Educators'), icon: User }
      ]
    }
  ];

  const saasAdminNavigation = [
    {
      name: 'Dashboard',
      icon: LayoutDashboard,
      href: createPageUrl('AdminDashboard')
    },
    {
      name: 'Benchmarking Database',
      icon: BarChart3,
      sections: [
        { name: 'Industry Benchmarks', href: createPageUrl('AdminBenchmarking'), icon: BarChart2 },
        { name: 'Category Keywords', href: createPageUrl('AdminCategoryKeywords'), icon: Tags }
      ]
    },
    {
      name: 'User Management',
      icon: Users,
      sections: [
        { name: 'Enterprise Accounts', href: createPageUrl('AdminEnterpriseAccounts'), icon: Building2 },
        { name: 'Accounts', href: createPageUrl('AdminAccounts'), icon: Building },
        { name: 'Users', href: createPageUrl('AdminUsers'), icon: User },
        { name: 'Subscriptions', href: createPageUrl('AdminSubscriptions'), icon: BadgeDollarSign }
      ]
    },
    {
      name: 'Learning Management',
      icon: GraduationCap,
      sections: [
        { name: 'Courses', href: createPageUrl('AdminCourses'), icon: BookOpen },
        { name: 'Educators', href: createPageUrl('AdminEducators'), icon: User },
        { name: 'Resources', href: createPageUrl('AdminResources'), icon: FileText },
        { name: 'Analytics', href: createPageUrl('AdminLearningAnalytics'), icon: BarChart3 }
      ]
    },
    {
      name: 'System Management',
      icon: Settings,
      sections: [
        { name: 'General Settings', href: createPageUrl('AdminSettings'), icon: Sliders },
        { name: 'API Monitoring', href: createPageUrl('AdminAPIMonitoring'), icon: Activity },
        { name: 'Integrations', href: createPageUrl('AdminIntegrations'), icon: RefreshCw },
        { name: 'Billing', href: createPageUrl('AdminBilling'), icon: CreditCard }
      ]
    },
    {
      name: 'Marketing',
      icon: Target,
      sections: [
        { name: 'Email Templates', href: createPageUrl('AdminEmailTemplates'), icon: Mail },
        { name: 'Landing Pages', href: createPageUrl('AdminLandingPages'), icon: LayoutIcon },
        { name: 'Campaigns', href: createPageUrl('AdminCampaigns'), icon: Megaphone }
      ]
    },
    {
      name: 'Support',
      icon: HeartHandshake,
      sections: [
        { name: 'Tickets', href: createPageUrl('AdminTickets'), icon: TicketCheck },
        { name: 'Knowledge Base', href: createPageUrl('AdminKnowledgeBase'), icon: BookOpen },
        { name: 'FAQs', href: createPageUrl('AdminFAQs'), icon: HelpCircle }
      ]
    }
  ];

  const getActiveNavigation = () => {
    if (window.location.pathname.includes('Admin')) {
      return saasAdminNavigation;
    }

    if (window.location.pathname.includes('Enterprise')) {
      return enterpriseNavigation;
    }

    if (viewMode === 'admin') {
      return saasAdminNavigation;
    } else if (viewMode === 'enterprise') {
      return enterpriseNavigation;
    } else {
      return filteredNavigation || businessNavigation;
    }
  };

  const registerItem = {
    icon: FilePlus,
    name: 'Register',
    href: createPageUrl('Register')
  };

  const publicPages = ['Register', 'register'];
  const currentPath = window.location.pathname.toLowerCase();
  const isRegisterPage = currentPath.includes('register');
  const isPublicPage = publicPages.includes(currentPageName) || isRegisterPage;

  const isReportEditorPage = location.pathname.includes('/ReportEditor');

  React.useEffect(() => {
    if (!isPublicPage) {
      loadUser();
    } else {
      setIsLoadingUser(false);
    }

    if (!isPublicPage) {
      const isAdminPage = window.location.pathname.includes('Admin');
      if (isAdminPage) {
        setViewMode('admin');
      }

      const isEnterprisePage = window.location.pathname.includes('Enterprise');
      if (isEnterprisePage) {
        setViewMode('enterprise');
      }
    }
  }, [isPublicPage]);

  const loadUser = async () => {
    try {
      setIsLoadingUser(true);
      const userData = await UserEntity.me();

      let accountsList = [];

      console.log("Loading user data:", {
        isEnterpriseAdmin: userData.is_enterprise_admin,
        enterprisePermissions: userData.enterprise_permissions,
        enterpriseAccountId: userData.enterprise_account_id,
        email: userData.email
      });

      if (userData.is_admin) {
        accountsList.push({
          id: "admin",
          name: "SAAS Admin",
          type: "admin",
          role: "admin"
        });
      }

      try {
        const enterpriseUsers = await EnterpriseUser.filter({
          user_id: userData.id
        });

        console.log("Enterprise User records:", enterpriseUsers);

        if (enterpriseUsers.length > 0) {
          const enterpriseAccounts = await EnterpriseAccount.list();
          console.log("All Enterprise Accounts:", enterpriseAccounts);

          for (const eu of enterpriseUsers) {
            const enterpriseAccount = enterpriseAccounts.find(ea => ea.id === eu.enterprise_account_id);
            if (enterpriseAccount) {
              accountsList.push({
                id: enterpriseAccount.id,
                name: enterpriseAccount.business_name || "Enterprise Account",
                type: "enterprise",
                role: eu.role || "enterprise_admin",
                isEnterprise: true
              });
            }
          }
        }

        if (userData.is_enterprise_admin && userData.enterprise_account_id) {
          const alreadyAdded = accountsList.some(a => a.id === userData.enterprise_account_id);

          if (!alreadyAdded) {
            try {
              const enterpriseAccount = await EnterpriseAccount.get(userData.enterprise_account_id);
              if (enterpriseAccount) {
                accountsList.push({
                  id: enterpriseAccount.id,
                  name: enterpriseAccount.business_name || "Enterprise Account",
                  type: "enterprise",
                  role: "enterprise_admin",
                  isEnterprise: true
                });
              }
            } catch (error) {
              console.error("Error fetching enterprise account:", error);
            }
          }
        }

        console.log("Enterprise accounts found:", accountsList.filter(a => a.type === 'enterprise'));

      } catch (error) {
        console.error("Error loading enterprise accounts:", error);
      }

      try {
        const allMedSpaAccounts = await MedSpaAccount.list();
        console.log("All available business accounts:", allMedSpaAccounts);

        let hasRealAccounts = false;

        if (userData.accounts && Array.isArray(userData.accounts)) {
          userData.accounts.forEach(userAccount => {
            if (!userAccount.isDemo && userAccount.account_id) {
              const matchingAccount = allMedSpaAccounts.find(a =>
                a.id && a.id.toString() === userAccount.account_id.toString()
              );

              if (matchingAccount) {
                accountsList.push({
                  id: matchingAccount.id,
                  name: matchingAccount.business_name || userAccount.account_name || 'Unknown Account',
                  type: 'business',
                  role: userAccount.role || 'user'
                });

                hasRealAccounts = true;
              }
            }
          });
        }

        const ownedAccounts = allMedSpaAccounts.filter(account =>
          account.owner_email && account.owner_email.toLowerCase() === userData.email.toLowerCase()
        );

        ownedAccounts.forEach(ownedAccount => {
          if (!accountsList.some(a => a.id === ownedAccount.id)) {
            accountsList.push({
              id: ownedAccount.id,
              name: ownedAccount.business_name || 'Unknown Account',
              type: 'business',
              role: 'owner'
            });
            hasRealAccounts = true;
          }
        });

        if (!hasRealAccounts && !userData.is_admin && accountsList.length === 0) {
          accountsList.push({
            id: "demo-1",
            name: "Demo Mode",
            type: "business",
            role: "owner",
            isDemo: true
          });
        }

        console.log("Final accounts list:", accountsList);
        setAccounts(accountsList);

        const storedAccountId = sessionStorage.getItem('accountId');
        const storedAccountType = sessionStorage.getItem('accountType');

        let selectedAccount;

        if (storedAccountId && storedAccountType) {
          selectedAccount = accountsList.find(a =>
            a.id.toString() === storedAccountId && a.type === storedAccountType
          );
        }

        if (!selectedAccount) {
          selectedAccount = accountsList.find(a => a.type === 'enterprise') ||
                          accountsList.find(a => a.type === 'admin') ||
                          accountsList[0];
        }

        if (selectedAccount) {
          setCurrentAccount(selectedAccount);
          setViewMode(selectedAccount.type);
          sessionStorage.setItem('accountId', selectedAccount.id.toString());
          sessionStorage.setItem('accountType', selectedAccount.type);
        }

      } catch (error) {
        console.error("Error loading accounts:", error);
        setAccounts([{
          id: "demo-1",
          name: "Demo Mode",
          type: "business",
          role: "owner",
          isDemo: true
        }]);
      }

      setUser(userData);
    } catch (error) {
      console.error("Error loading user:", error);
      setUser(null);
    } finally {
      setIsLoadingUser(false);
    }
  };

  const isDemoMode = currentAccount?.isDemo;

  const getNavigationItems = () => {
    let items = [...businessNavigation];

    if (isDemoMode) {
      items.unshift({
        name: 'Account Setup',
        icon: Building2,
        href: createPageUrl('AccountSetup'),
        highlight: true
      });
    }

    return items;
  };

  const handleLogout = async () => {
    try {
      await UserEntity.logout();
      window.location.reload();
    } catch (error) {
      console.error("Error logging out:", error);
    }
  };

  const handleAccountSwitch = (account) => {
    sessionStorage.setItem('accountId', account.id.toString());
    sessionStorage.setItem('accountType', account.type);
    sessionStorage.setItem('accountName', account.name);
    sessionStorage.setItem('accountRole', account.role);

    setCurrentAccount(account);
    setAccountSwitcherOpen(false);

    const newViewMode = account.type === 'admin' ? 'admin' :
                       account.type === 'enterprise' ? 'enterprise' : 'business';
    setViewMode(newViewMode);
    sessionStorage.setItem('viewMode', newViewMode);

    if (account.type === 'admin') {
      window.location.href = createPageUrl('AdminDashboard');
    } else if (account.type === 'enterprise') {
      window.location.href = createPageUrl('EnterpriseDashboard');
    } else {
      window.location.href = createPageUrl('Dashboard');
    }
  };

  const renderAccountSwitcher = () => {
    const standardAccounts = accounts
      .filter(acc => acc.type === 'business')
      .sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    const adminAccounts = accounts
      .filter(acc => acc.type === 'admin')
      .sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    const enterpriseAccounts = accounts
      .filter(acc => acc.type === 'enterprise')
      .sort((a, b) => (a.name || '').localeCompare(b.name || ''));

    console.log("Enterprise accounts in switcher (sorted):", enterpriseAccounts);

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className={cn(
              "flex items-center gap-2 w-full text-left justify-start hover:bg-gray-50 px-3",
              sidebarCollapsed ? "justify-center" : ""
            )}
          >
            <div className={cn(
              "flex items-center gap-2 truncate",
              sidebarCollapsed ? "" : "w-full"
            )}>
              {currentAccount?.type === "admin" ? (
                <Shield className="h-5 w-5 text-purple-500" />
              ) : currentAccount?.type === "enterprise" ? (
                <Building2 className="h-5 w-5 text-indigo-500" />
              ) : (
                <Building2 className="h-5 w-5 text-gray-500" />
              )}

              {!sidebarCollapsed && (
                <div className="flex justify-between items-center w-full">
                  <div className="text-sm font-medium truncate">
                    {currentAccount?.name || "Select Account"}
                  </div>
                  <ChevronDown className="h-4 w-4 text-gray-500" />
                </div>
              )}
            </div>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-64">
          {adminAccounts.length > 0 && (
            <>
              <DropdownMenuLabel className="text-purple-600">Admin Dashboard</DropdownMenuLabel>
              {adminAccounts.map(account => (
                <DropdownMenuItem
                  key={account.id}
                  className={cn(
                    "cursor-pointer",
                    account.id === currentAccount?.id && "bg-gray-100"
                  )}
                  onClick={() => handleAccountSwitch(account)}
                >
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4 text-purple-500" />
                    <span>{account.name}</span>
                  </div>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
            </>
          )}

          {enterpriseAccounts.length > 0 && (
            <>
              <DropdownMenuLabel className="text-indigo-600">Enterprise Dashboards</DropdownMenuLabel>
              {enterpriseAccounts.map(account => (
                <DropdownMenuItem
                  key={account.id}
                  className={cn(
                    "cursor-pointer",
                    account.id === currentAccount?.id && "bg-gray-100"
                  )}
                  onClick={() => handleAccountSwitch(account)}
                >
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-indigo-500" />
                    <span>{account.name}</span>
                  </div>
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
            </>
          )}

          <DropdownMenuLabel>Business Accounts</DropdownMenuLabel>
          {standardAccounts.map(account => (
            <DropdownMenuItem
              key={account.id}
              className={cn(
                "cursor-pointer",
                account.id === currentAccount?.id && "bg-gray-100"
              )}
              onClick={() => handleAccountSwitch(account)}
            >
              <div className="flex items-center gap-2">
                <Building2 className="h-4 w-4 text-gray-500" />
                <span>{account.name}</span>
                {account.isDemo && (
                  <Badge variant="outline" className="ml-2 px-1 py-0 text-xs border-orange-200 bg-orange-50 text-orange-600">
                    Demo
                  </Badge>
                )}
              </div>
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  const handleAccountChange = (account) => {
    setCurrentAccount(account);

    if (account.type === "admin") {
      setViewMode("admin");
      window.location.href = createPageUrl('AdminDashboard');
    } else {
      setViewMode("business");

      sessionStorage.setItem('accountId', account.id);
      sessionStorage.setItem('accountType', account.type);
      sessionStorage.setItem('accountName', account.name);
      sessionStorage.setItem('accountRole', account.role);

      if (!window.location.pathname.includes('Dashboard')) {
        window.location.href = createPageUrl('Dashboard');
      } else {
        window.location.reload();
      }
    }
  };

  const renderNavItem = (item) => {
    const isTourItem = ['Dashboard', 'My Business', 'Marketing', 'Sales', 'Operations', 'Reporting', 'Learning', 'Settings'].includes(item.name);

    return (
      <div key={item.name} className="mb-1">
        {item.sections ? (
          <Collapsible
            open={expandedSection === item.name}
            onOpenChange={() => setExpandedSection(expandedSection === item.name ? null : item.name)}
          >
            <CollapsibleTrigger className={cn(
              "flex items-center w-full rounded-lg hover:bg-gray-50",
              sidebarCollapsed ? "px-2 py-2 justify-center" : "px-4 py-2 justify-between"
            )}>
              <div className="flex items-center gap-2">
                <item.icon className="w-5 h-5 text-gray-600" />
                {!sidebarCollapsed && <span>{item.name}</span>}
              </div>
              {!sidebarCollapsed && (
                expandedSection === item.name ?
                  <ChevronUp className="w-4 h-4" /> :
                  <ChevronDown className="w-4 h-4" />
              )}
            </CollapsibleTrigger>
            {!sidebarCollapsed && (
              <CollapsibleContent className="space-y-1 mt-1">
                {item.sections.map((section) => (
                  <Link
                    key={section.name}
                    to={section.href}
                    className={cn(
                      "flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-lg ml-4",
                      currentPageName === section.name && "bg-blue-50 text-blue-700"
                    )}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <section.icon className="w-4 h-4" />
                    <span className="text-sm">{section.name}</span>
                  </Link>
                ))}
              </CollapsibleContent>
            )}
          </Collapsible>
        ) : (
          <Link
            to={item.href}
            onClick={async (e) => {
              if (isTourItem && showOnboardingWizard) {
                const tourTaken = await handleNavItemClickForTour(item.name, item.href);
                if (tourTaken) {
                  e.preventDefault();
                } else {
                   setSidebarOpen(false);
                }
              } else {
                setSidebarOpen(false);
              }
            }}
            className={cn(
              "flex items-center gap-3 rounded-lg hover:bg-gray-100 transition-colors duration-150 ease-in-out",
              item.highlight && "bg-purple-50 hover:bg-purple-100 ring-2 ring-purple-300",
              sidebarCollapsed ? "px-2 py-2 justify-center" : "px-4 py-2",
              currentPageName === item.name && !item.highlight && "bg-gray-100 text-gray-900 font-medium",
              currentPageName === item.name && item.highlight && "bg-purple-100 text-purple-700 font-medium",
              activeTour.section === item.name && "ring-2 ring-[#717BFF] shadow-lg"
            )}
          >
            <item.icon className={cn("w-5 h-5", item.highlight ? "text-purple-600" : "text-gray-600", currentPageName === item.name && "text-gray-900")} />
            {!sidebarCollapsed && <span className={cn(item.highlight ? "text-purple-700" : "text-gray-700", currentPageName === item.name && "text-gray-900")}>{item.name}</span>}
          </Link>
        )}
      </div>
    );
  };

  const handleNavItemClickForTour = async (itemName, itemPage) => {
    setSidebarOpen(false);

    if (!showOnboardingWizard || !onboardingStatusData || !onboardingStatusData.wizard_steps) return;

    const tourCopy = {
      'Dashboard': "You're currently in the dashboard where you'll get a top-level view of how your business is doing. Here you can track marketing efforts, sales efforts, and revenue. Click on any of the tabs on the sidebar left to get a tour of each.",
      'My Business': "In this area you can track your individual business goals, benchmarking statistics against the industry, and set a roadmap for what you'd like to achieve.",
      'Marketing': "In this area you can track anything related to getting new eyeballs to see your business. You can track Website performance, Ad Performance, Social Media, Referral Partners, Google My business, and SEO.",
      'Sales': "In this area, you've got a robust CRM to track sales performance and conversion rate of the team in charge of taking leads and turning them into revenue.",
      'Operations': "In this area, you really get a pulse on how your clinic is doing. Here you can track overall revenue, retention rate of clients, product sales rate, staff member utilization rate, as well as implement a daily close report.",
      'Reporting': "This is where you can see the history of all of your reports as well as configure your daily email that delivers your performance metrics.",
      'Learning': "In this area, we have courses to teach you how to grow your clinic even further, as well as trusted advisors that we believe can help out your business.",
      'Settings': "The settings allow you to log into any of your data sources at any time as well as make adjustments to your account.",
    };

    const stepKeyMap = {
      'Dashboard': 'app_overview_dashboard_visited',
      'My Business': 'app_overview_my_business_visited',
      'Marketing': 'app_overview_marketing_visited',
      'Sales': 'app_overview_sales_visited',
      'Operations': 'app_overview_service_visited', // Mapped from old Service
      'Reporting': 'app_overview_reporting_visited',
      'Learning': 'app_overview_learning_visited',
      'Settings': 'app_overview_settings_visited',
    };

    const currentStepKey = stepKeyMap[itemName];

    if (tourCopy[itemName] && currentStepKey && !onboardingStatusData.wizard_steps[currentStepKey]) {
      setActiveTour({ section: itemName, message: tourCopy[itemName], pageToNavigate: itemPage });
      return true;
    }
    return false;
  };

  const closeTourModal = async (markComplete) => {
    const pageToNavigate = activeTour.pageToNavigate;
    const sectionName = activeTour.section;
    setActiveTour({ section: null, message: '', pageToNavigate: null });

    if (markComplete && onboardingStatusData && sectionName) {
      const stepKeyMap = {
          'Dashboard': 'app_overview_dashboard_visited',
          'My Business': 'app_overview_my_business_visited',
          'Marketing': 'app_overview_marketing_visited',
          'Sales': 'app_overview_sales_visited',
          'Operations': 'app_overview_service_visited',
          'Reporting': 'app_overview_reporting_visited',
          'Learning': 'app_overview_learning_visited',
          'Settings': 'app_overview_settings_visited',
      };
      const stepKey = stepKeyMap[sectionName];
      if (stepKey) {
        try {
          const newWizardSteps = { ...onboardingStatusData.wizard_steps, [stepKey]: true };
          const updatedStatus = await OnboardingStatus.update(onboardingStatusData.id, { wizard_steps: newWizardSteps });
          setOnboardingStatusData(updatedStatus);
        } catch (error) {
          console.error("Error updating onboarding status from tour modal:", error);
        }
      }
    }
    if (pageToNavigate) {
        window.location.href = pageToNavigate;
    }
  };

  const handleWelcomeVideoClose = async (watched) => {
    setShowWelcomeVideo(false);
    if (watched && onboardingStatusData) {
      try {
        const newWizardSteps = { ...onboardingStatusData.wizard_steps, welcome_video_watched: true };
        const updatedStatus = await OnboardingStatus.update(onboardingStatusData.id, { wizard_steps: newWizardSteps });
        setOnboardingStatusData(updatedStatus);
      } catch(error) {
        console.error("Error updating welcome_video_watched:", error)
      }
    }
  };

  React.useEffect(() => {
    if (user && currentAccount) {
      loadOnboardingStatus(user, currentAccount);
    }
  }, [user, currentAccount, currentPageName]);

  React.useEffect(() => {
    const handleStatusChange = () => {
      if (user && currentAccount) {
        loadOnboardingStatus(user, currentAccount);
      }
    };
    window.addEventListener('onboardingStatusChanged', handleStatusChange);
    return () => window.removeEventListener('onboardingStatusChanged', handleStatusChange);
  }, [user, currentAccount]);

  const loadOnboardingStatus = async (currentUser, currentAccount) => {
    if (!currentUser || !currentAccount) {
      setShowOnboardingWizard(false);
      setOnboardingStatusData(null);
      return;
    }
    try {
      let records = await OnboardingStatus.filter({ user_id: currentUser.id, account_id: currentAccount.id });
      let status;
      if (records.length > 0) {
        status = records[0];
      } else {
        status = await OnboardingStatus.create({
          user_id: currentUser.id,
          account_id: currentAccount.id,
          is_wizard_active: true,
          wizard_steps: { }
        });
      }
      setOnboardingStatusData(status);
      setShowOnboardingWizard(status.is_wizard_active);

      if (status.is_wizard_active && (!status.wizard_steps || !status.wizard_steps.welcome_video_watched)) {
         if (currentPageName === 'Dashboard' || currentPageName === 'OnboardingWizard') {
            setShowWelcomeVideo(true);
        }
      }

    } catch (error) {
      console.error("Error fetching/creating onboarding status in Layout:", error);
      setShowOnboardingWizard(false);
    }
  };

  const activeNav = getActiveNavigation();
  console.log("Current view mode:", viewMode);
  console.log("On admin page:", window.location.pathname.includes('Admin'));
  console.log("Current URL:", window.location.pathname);
  console.log("Using navigation:", activeNav === saasAdminNavigation ? 'Admin Navigation' : 'Business Navigation');

  const isAdminPage = window.location.pathname.includes('Admin');
  const isEnterpriseAdminPage = window.location.pathname.includes('Enterprise') && !isAdminPage;

  if (isPublicPage) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">

        {children}
      </div>
    );
  }

  if (isLoadingUser) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100">

        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#8890ea]"></div>
      </div>
    );
  }

  if (!user && !isPublicPage) {
    window.location.href = "/login";
    return null;
  }

  if (isReportEditorPage) {
    return (
      <div className="flex flex-col h-screen bg-[#FAFBFC]">

        {children}
      </div>
    );
  }

  if (isAdminPage) {
    const activeNav = saasAdminNavigation;
    return (
      <div className="flex h-screen bg-[#FAFBFC]">


        <aside className={cn(
          "fixed top-4 left-4 z-50 h-[calc(100vh-32px)] bg-white border border-gray-100 transform transition-all duration-300 ease-in-out md:relative flex flex-col",
          "rounded-2xl shadow-sm",
          sidebarCollapsed ? "w-20" : "w-72",
          sidebarOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        )}>
          <div className={cn(
            "flex items-center border-b border-gray-50",
            sidebarCollapsed ? "justify-center p-4" : "px-6 py-4"
          )}>
            {sidebarCollapsed ? (
              <img
                src="https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/4f104e_Quanti_Icon_GreyLav.png"
                alt="QuantiMD Icon"
                className="h-8 w-8"
              />
            ) : (
              <div className="flex items-center gap-2">
                <img
                  src="https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/55089b_QuantiMD_Logo.png"
                  alt="QuantiMD Logo"
                  className="h-8"
                />
                <Badge className="bg-purple-100 text-purple-700">ADMIN</Badge>
              </div>
            )}
          </div>

          <div className={cn(
            "px-4 py-3 border-b border-gray-50",
            sidebarCollapsed && "px-2"
          )}>
            {renderAccountSwitcher()}
          </div>

          <nav className="flex-1 overflow-y-auto p-4">
            <div className="space-y-2">
              {activeNav.map((item) => renderNavItem(item))}
            </div>
          </nav>

          <div className="p-4 border-t border-gray-100">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-purple-100 text-purple-700">
                  {user?.full_name?.[0] || user?.email[0].toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {!sidebarCollapsed && (
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{user?.full_name || 'Admin'}</p>
                  <p className="text-xs text-gray-500 truncate">{user?.email}</p>
                </div>
              )}
            </div>
          </div>
        </aside>

        <div className="flex-1 flex flex-col min-h-screen overflow-hidden">
          <main className="flex-1 overflow-auto bg-[#FAFBFC]">
            <div className="container mx-auto p-6">
              {children}
            </div>
          </main>
        </div>
      </div>
    );
  }

  if (isEnterpriseAdminPage) {
    return (
      <div className="flex h-screen bg-[#FAFBFC]">


        <aside className={cn(
          "fixed top-4 left-4 z-50 h-[calc(100vh-32px)] bg-white border border-gray-100 transform transition-all duration-300 ease-in-out md:relative flex flex-col",
          "rounded-2xl shadow-sm",
          sidebarCollapsed ? "w-20" : "w-72",
          sidebarOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        )}>
          <div className={cn(
            "flex items-center border-b border-gray-50",
            sidebarCollapsed ? "justify-center p-4" : "px-6 py-4"
          )}>
            {sidebarCollapsed ? (
              <img
                src="https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/4f104e_Quanti_Icon_GreyLav.png"
                alt="QuantiMD Icon"
                className="h-8 w-8"
              />
            ) : (
              <div className="flex items-center gap-2">
                <img
                  src="https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/55089b_QuantiMD_Logo.png"
                  alt="QuantiMD Logo"
                  className="h-8"
                />
                <Badge className="bg-purple-100 text-purple-700">ENTERPRISE</Badge>
              </div>
            )}
          </div>

          <div className={cn(
            "px-4 py-3 border-b border-gray-50",
            sidebarCollapsed && "px-2"
          )}>
            {renderAccountSwitcher()}
          </div>

          <nav className="flex-1 overflow-y-auto p-4">
            <div className="space-y-2">
              <Link
                to={createPageUrl('EnterpriseDashboard')}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-gray-50",
                  currentPageName === 'EnterpriseDashboard' && "bg-purple-50 text-purple-700"
                )}
              >
                <LayoutDashboard className="w-5 h-5" />
                {!sidebarCollapsed && "Dashboard"}
              </Link>

              <Link
                to={createPageUrl('EnterpriseBenchmarking')}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-gray-50",
                  currentPageName === 'EnterpriseBenchmarking' && "bg-purple-50 text-purple-700"
                )}
              >
                <BarChart2 className="w-5 h-5" />
                {!sidebarCollapsed && "Benchmarking"}
              </Link>

              <Link
                to={createPageUrl('EnterpriseGoalTracking')}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-gray-50",
                  currentPageName === 'EnterpriseGoalTracking' && "bg-purple-50 text-purple-700"
                )}
              >
                <Target className="w-5 h-5" />
                {!sidebarCollapsed && "Goal Tracking"}
              </Link>

              <Link
                to={createPageUrl('EnterpriseAccounts')}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-gray-50",
                  currentPageName === 'EnterpriseAccounts' && "bg-purple-50 text-purple-700"
                )}
              >
                <Building2 className="w-5 h-5" />
                {!sidebarCollapsed && "Sub-accounts"}
              </Link>

              <Link
                to={createPageUrl('EnterpriseUsers')}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-gray-50",
                  currentPageName === 'EnterpriseUsers' && "bg-purple-50 text-purple-700"
                )}
              >
                <Users className="w-5 h-5" />
                {!sidebarCollapsed && "Users"}
              </Link>

              <Link
                to={createPageUrl('EnterpriseLearning')}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-gray-50",
                  currentPageName === 'EnterpriseLearning' && "bg-purple-50 text-purple-700"
                )}
              >
                <GraduationCap className="w-5 h-5" />
                {!sidebarCollapsed && "Learning"}
              </Link>

              <Link
                to={createPageUrl('EnterpriseAnalytics')}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-gray-50",
                  currentPageName === 'EnterpriseAnalytics' && "bg-purple-50 text-purple-700"
                )}
              >
                <BarChart3 className="w-5 h-5" />
                {!sidebarCollapsed && "Analytics"}
              </Link>

              <Link
                to={createPageUrl('EnterpriseSettings')}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 rounded-lg hover:bg-gray-50",
                  currentPageName === 'EnterpriseSettings' && "bg-purple-50 text-purple-700"
                )}
              >
                <Settings className="w-5 h-5" />
                {!sidebarCollapsed && "Settings"}
              </Link>
            </div>
          </nav>

          <div className="p-4 border-t border-gray-100">
            <div className="flex items-center gap-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback className="bg-purple-100 text-purple-700">
                  {user?.full_name?.[0] || user?.email[0].toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {!sidebarCollapsed && (
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{user?.full_name || 'Enterprise Admin'}</p>
                  <p className="text-xs text-gray-500 truncate">{user?.email}</p>
                </div>
              )}
            </div>
          </div>
        </aside>

        <div className="flex-1 flex flex-col min-h-screen overflow-hidden">
          <main className="flex-1 overflow-auto bg-[#FAFBFC]">
            <div className="container mx-auto p-6">
              {children}
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-screen bg-[#FAFBFC]">


      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 md:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <aside className={cn(
        "fixed top-4 left-4 z-50 h-[calc(100vh-32px)] bg-white border border-gray-100 transform transition-all duration-300 ease-in-out md:relative flex flex-col",
        "rounded-2xl shadow-sm",
        sidebarCollapsed ? "w-20" : "w-72",
        sidebarOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
      )}>
        <button
          onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
          className={cn(
            "absolute -right-3 top-1/2 -translate-y-1/2 w-6 h-6",
            "bg-white border border-gray-200 rounded-full shadow-sm",
            "flex items-center justify-center",
            "hover:bg-gray-50 transition-colors",
            "focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2",
            "md:flex hidden"
          )}
        >
          {sidebarCollapsed ? (
            <ChevronRight className="w-4 h-4 text-gray-600" />
          ) : (
            <ChevronLeft className="w-4 h-4 text-gray-600" />
          )}
        </button>

        <div className={cn(
          "flex items-center border-b border-gray-50",
          sidebarCollapsed ? "justify-center p-4" : "px-6 py-4"
        )}>
          {sidebarCollapsed ? (
            <img
              src="https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/4f104e_Quanti_Icon_GreyLav.png"
              alt="QuantiMD Icon"
              className="h-8 w-8"
            />
          ) : (
            <img
              src="https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/55089b_QuantiMD_Logo.png"
              alt="QuantiMD Logo"
              className="h-8"
            />
          )}
          {!sidebarCollapsed && currentAccount?.type === 'admin' && (
            <Badge className="ml-2 bg-purple-100 text-purple-700">ADMIN</Badge>
          )}
           {!sidebarCollapsed && currentAccount?.type === 'enterprise' && (
            <Badge className="ml-2 bg-indigo-100 text-indigo-700">ENTERPRISE</Badge>
          )}
        </div>

        <div className={cn(
          "px-4 py-3 border-b border-gray-50",
          sidebarCollapsed && "px-2"
        )}>
          {renderAccountSwitcher()}
        </div>

        <nav className={cn(
          "flex-1 overflow-y-auto",
          sidebarCollapsed ? "px-2 py-2" : "px-4 py-2"
        )}>
          <Link
            to={createPageUrl('AskQuanti')}
            className={cn(
              "flex items-center gap-2 mb-4 rounded-xl bg-[#717BFF] hover:bg-[#6269e3] transition-colors",
              sidebarCollapsed ? "px-2 py-3 justify-center" : "px-4 py-3"
            )}
          >
            <img
              src="https://qtrypzzcjebvfcihiynt.supabase.co/storage/v1/object/public/base44-prod/public/748061_QuantiIcon.png"
              alt="Ask Quanti Icon"
              className="w-5 h-5"
            />
            {!sidebarCollapsed && (
              <div className="text-white">
                <div className="font-medium text-white !text-white" style={{color: 'white !important'}}>Ask Quanti</div>
                <div className="text-xs text-white opacity-90">AI-powered business consulting</div>
              </div>
            )}
          </Link>

          <div className="space-y-1">
            {getActiveNavigation().map((item, index) => (
              <React.Fragment key={index}>
                {renderNavItem(item)}
              </React.Fragment>
            ))}
          </div>
        </nav>

        <div className="mt-auto border-t border-gray-100">
          <Collapsible
            open={settingsOpen}
            onOpenChange={setSettingsOpen}
            className="space-y-2"
          >
            <CollapsibleTrigger className={cn(
              "flex items-center rounded-xl hover:bg-gray-50 w-full",
              sidebarCollapsed ? "p-2 justify-center" : "px-4 py-2 justify-between"
            )}>
              <div className="flex items-center gap-2">
                <Settings className="w-5 h-5 text-gray-600" />
                {!sidebarCollapsed && <span>Settings</span>}
              </div>
              {!sidebarCollapsed && (
                settingsOpen ? (
                  <ChevronUp className="w-4 h-4" />
                ) : (
                  <ChevronDown className="w-4 h-4" />
                )
              )}
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-1 pl-4">
              {settingsNavigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    "flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-xl",
                    currentPageName === item.name && "bg-purple-100 text-purple-700"
                  )}
                >
                  <item.icon className="w-4 h-4" />
                  {item.name}
                </Link>
              ))}
              <Link
                to={registerItem.href}
                className={cn(
                  "flex items-center gap-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-xl",
                  currentPageName === registerItem.name && "bg-purple-100 text-purple-700"
                )}
              >
                <registerItem.icon className="w-4 h-4" />
                {registerItem.name}
              </Link>
            </CollapsibleContent>
          </Collapsible>

          {user && (
            <div className={cn(
              "mt-4 pt-4 border-t border-gray-100",
              sidebarCollapsed ? "text-center flex flex-col items-center" : ""
            )}>
              {!sidebarCollapsed ? (
                <div className="px-4 py-2">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="bg-purple-100 text-purple-700">
                        {user.full_name?.[0] || user.email[0].toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {user.full_name || 'User'}
                      </p>
                      <p className="text-xs text-gray-500 truncate">
                        {user.email}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    className="w-full mt-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-xl"
                    onClick={handleLogout}
                  >
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign out
                  </Button>
                </div>
              ) : (
                <div className="flex flex-col items-center gap-2">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-purple-100 text-purple-700">
                      {user.full_name?.[0] || user.email[0].toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 rounded-xl w-8 h-8 p-0 flex items-center justify-center"
                    onClick={handleLogout}
                  >
                    <LogOut className="w-4 h-4" />
                  </Button>
                </div>
              )}
            </div>
          )}
        </div>
      </aside>

      <div className="flex-1 flex flex-col min-h-screen overflow-hidden">
        <header className="md:hidden bg-white border-b px-4 py-3">
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </Button>
        </header>

        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-[#FAFBFC]">
          <div className="container mx-auto p-6">
            {children}
          </div>
        </main>
        <Toaster />
        <style jsx global>{`
          .text-editor-in-modal .ql-container {
            height: calc(100% - 42px) !important;
            min-height: 250px;
            font-size: 16px;
          }
          .text-editor-in-modal .ql-editor {
            min-height: 250px;
            font-size: 1rem;
          }
          .text-editor-in-modal .ql-toolbar {
            position: sticky;
            top: 0;
            background-color: white;
            z-index: 1;
            border-bottom: 1px solid #ccc;
          }
        `}</style>
      </div>

      {currentPageName !== 'AskQuanti' && currentPageName !== 'OnboardingWizard' && <QuantiChatBubble />}

      {showWelcomeVideo && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[100]">
          <Card className="w-full max-w-2xl shadow-2xl">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Welcome to QuantiMD!</CardTitle>
              <Button variant="ghost" size="icon" onClick={() => handleWelcomeVideoClose(false)}><X className="w-5 h-5" /></Button>
            </CardHeader>
            <CardContent>
              <p className="mb-4">Here's a quick introduction to the platform.</p>
              <div className="aspect-video bg-gray-200 rounded-lg flex items-center justify-center">
                <iframe
                    src="https://player.vimeo.com/video/123087195?h=1ac519535a"
                    width="100%"
                    height="100%"
                    frameBorder="0"
                    allow="autoplay; fullscreen; picture-in-picture"
                    allowFullScreen
                    title="QuantiMD Welcome Video">
                </iframe>
              </div>
              <Button onClick={() => handleWelcomeVideoClose(true)} className="w-full mt-6 bg-[#717BFF] hover:bg-[#6269e3]">
                I've Watched It / Skip
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {activeTour.section && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[100]">
          <Card className="w-full max-w-lg shadow-2xl">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Tour: {activeTour.section}</CardTitle>
               <Button variant="ghost" size="icon" onClick={() => closeTourModal(false)}><X className="w-5 h-5" /></Button>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 mb-6">{activeTour.message}</p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => closeTourModal(false)}>Skip Section</Button>
                <Button onClick={() => closeTourModal(true)} className="bg-[#717BFF] hover:bg-[#6269e3]">
                  Got it! Next
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}

