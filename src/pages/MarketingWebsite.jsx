
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Users,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  Target,
  UserPlus
} from 'lucide-react';
import { GoogleAnalyticsData } from '@/api/entities';
import WebsiteTrafficChart from '../components/marketing/website/WebsiteTrafficChart';
import TopTrafficSources from '../components/marketing/website/TopTrafficSources';
import EngagementMetrics from '../components/marketing/website/EngagementMetrics';
import DeviceBreakdown from '../components/marketing/website/DeviceBreakdown';
import DateRangeSelector from '../components/business/DateRangeSelector';
import { subDays } from 'date-fns';
import { parseGoogleAnalyticsData } from '@/utils/dataParser';

export default function MarketingWebsite() {
  const [dateRange, setDateRange] = useState({ 
    from: subDays(new Date(), 29), 
    to: new Date() 
  });
  const [historicalData, setHistoricalData] = useState([]);
  const [periodData, setPeriodData] = useState(null);
  const [previousPeriodData, setPreviousPeriodData] = useState(null);
  const [currentPeriodRecords, setCurrentPeriodRecords] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch up to a year of data on component mount
  useEffect(() => {
    const fetchHistoricalData = async () => {
      setIsLoading(true);
      try {
        const accountId = sessionStorage.getItem('accountId');
        if (!accountId) {
          setIsLoading(false);
          return;
        }
        const rawData = await GoogleAnalyticsData.filter({ account_id: accountId }, '-date', 365);
        // Parse JSON string fields in each data record
        const parsedData = rawData.map(record => parseGoogleAnalyticsData(record));
        setHistoricalData(parsedData);
      } catch (error) {
        console.error("Failed to fetch historical analytics data:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchHistoricalData();
  }, []);

  // Aggregate data when dateRange or historicalData changes
  useEffect(() => {
    if (historicalData.length === 0) return;

    const aggregateData = (dataArray) => {
      if (!dataArray || dataArray.length === 0) {
        return {
          total_users: 0, total_sessions: 0, conversions: 0, engagement_rate: 0,
          sessions_by_device: [], conversions_by_device: [], sessions_by_source: [],
        };
      }

      const metrics = {
        total_users: 0, total_sessions: 0, conversions: 0, engagement_rate_total: 0,
        sessions_by_device: {}, conversions_by_device: {}, sessions_by_source: {},
      };

      dataArray.forEach(day => {
        metrics.total_users += day.total_users || 0;
        metrics.total_sessions += day.total_sessions || 0;
        metrics.conversions += day.conversions || 0;
        metrics.engagement_rate_total += (day.engagement_rate || 0) * (day.total_sessions || 0);

        (day.sessions_by_device || []).forEach(d => {
          metrics.sessions_by_device[d.device] = (metrics.sessions_by_device[d.device] || 0) + d.sessions;
        });
        (day.conversions_by_device || []).forEach(d => {
          metrics.conversions_by_device[d.device] = (metrics.conversions_by_device[d.device] || 0) + d.conversions;
        });
        (day.sessions_by_source || []).forEach(s => {
          metrics.sessions_by_source[s.source] = (metrics.sessions_by_source[s.source] || 0) + s.sessions;
        });
      });

      metrics.engagement_rate = metrics.total_sessions > 0 ? metrics.engagement_rate_total / metrics.total_sessions : 0;
      
      metrics.sessions_by_device = Object.entries(metrics.sessions_by_device).map(([device, sessions]) => ({ device, sessions }));
      metrics.conversions_by_device = Object.entries(metrics.conversions_by_device).map(([device, conversions]) => ({ device, conversions }));
      metrics.sessions_by_source = Object.entries(metrics.sessions_by_source).map(([source, sessions]) => ({ source, sessions })).sort((a,b) => b.sessions - a.sessions);

      return metrics;
    };
    
    const fromDate = new Date(dateRange.from);
    fromDate.setHours(0, 0, 0, 0);

    const toDate = new Date(dateRange.to);
    toDate.setHours(23, 59, 59, 999);
    
    const records = historicalData.filter(d => {
        // Fix: Treat date string as local to prevent timezone issues
        const recordDate = new Date(`${d.date}T00:00:00`);
        return recordDate >= fromDate && recordDate <= toDate;
    });
    setCurrentPeriodRecords(records);

    const diffInDays = Math.round((toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24));
    
    const prevRangeEnd = subDays(fromDate, 1);
    prevRangeEnd.setHours(23,59,59,999);

    const prevRangeStart = subDays(prevRangeEnd, diffInDays-1);
    prevRangeStart.setHours(0,0,0,0);
    
    const previousRecords = historicalData.filter(d => {
        // Fix: Treat date string as local to prevent timezone issues
        const recordDate = new Date(`${d.date}T00:00:00`);
        return recordDate >= prevRangeStart && recordDate <= prevRangeEnd;
    });

    setPeriodData(aggregateData(records));
    setPreviousPeriodData(aggregateData(previousRecords));

  }, [dateRange, historicalData]);


  const calculateChange = (current, previous) => {
    if (previous === 0 || previous === null || current === null) return { percentage: 'N/A', direction: 'neutral' };
    if (current === previous) return { percentage: '0.0%', direction: 'neutral' };
    const change = ((current - previous) / previous) * 100;
    return {
      percentage: `${Math.abs(change).toFixed(1)}%`,
      direction: change >= 0 ? 'increase' : 'decrease'
    };
  };

  const renderChange = (change) => {
    if (change.percentage === 'N/A') {
      return <div className="text-gray-500 text-sm">vs. previous period</div>;
    }
    const isIncrease = change.direction === 'increase';
    return (
      <div className={`flex items-center ${isIncrease ? 'text-green-600' : 'text-red-600'} text-sm`}>
        {isIncrease ? <ArrowUpRight className="h-4 w-4 mr-1" /> : <ArrowDownRight className="h-4 w-4 mr-1" />}
        {change.percentage} {change.direction}
      </div>
    );
  };

  const sessionsChange = calculateChange(periodData?.total_sessions, previousPeriodData?.total_sessions);
  const usersChange = calculateChange(periodData?.total_users, previousPeriodData?.total_users);
  const conversionChange = calculateChange(
    periodData?.total_sessions > 0 ? (periodData.conversions || 0) / periodData.total_sessions : 0, 
    previousPeriodData?.total_sessions > 0 ? (previousPeriodData.conversions || 0) / previousPeriodData.total_sessions : 0
  );
  const leadsChange = calculateChange(periodData?.conversions, previousPeriodData?.conversions);

  if (isLoading) {
    return <div className="p-6">Loading Website Analytics...</div>;
  }
  
  return (
    <div className="p-6 bg-[#FAFBFC] min-h-screen">
      <div className="max-w-7xl mx-auto space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div className="bg-white p-6 rounded-2xl shadow-sm">
            <h1 className="text-2xl font-bold text-gray-900">Website Analytics</h1>
            <p className="text-gray-500">
              Performance for the selected period
            </p>
          </div>
          <DateRangeSelector onRangeChange={setDateRange} />
        </div>

        {/* Main Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Total Sessions
              </CardTitle>
              <Activity className="h-4 w-4 text-[#8884FF]" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{periodData?.total_sessions?.toLocaleString() || 0}</div>
              {renderChange(sessionsChange)}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Total Users
              </CardTitle>
              <Users className="h-4 w-4 text-[#A594FF]" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{periodData?.total_users?.toLocaleString() || 0}</div>
              {renderChange(usersChange)}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Conversion Rate
              </CardTitle>
              <Target className="h-4 w-4 text-[#8884FF]" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {periodData?.total_sessions > 0 ? ((periodData.conversions || 0) / periodData.total_sessions * 100).toFixed(1) + '%' : '0.0%'}
              </div>
               {renderChange(conversionChange)}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-500">
                Total Leads
              </CardTitle>
              <UserPlus className="h-4 w-4 text-[#A594FF]" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{periodData?.conversions || 0}</div>
              {renderChange(leadsChange)}
            </CardContent>
          </Card>
        </div>

        <div className="grid lg:grid-cols-6 gap-6">
          {/* Traffic Overview Chart */}
          <Card className="lg:col-span-4">
            <CardHeader>
              <CardTitle>Traffic Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <WebsiteTrafficChart data={currentPeriodRecords} />
            </CardContent>
          </Card>

          {/* Top Traffic Sources */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Top Traffic Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <TopTrafficSources currentPeriodData={periodData} previousPeriodData={previousPeriodData} />
            </CardContent>
          </Card>

          {/* Device Breakdown for Sessions */}
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle>Sessions by Device</CardTitle>
            </CardHeader>
            <CardContent>
              <DeviceBreakdown data={periodData?.sessions_by_device} type="Sessions" />
            </CardContent>
          </Card>

          {/* Device Breakdown for Conversions */}
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle>Conversions by Device</CardTitle>
            </CardHeader>
            <CardContent>
              <DeviceBreakdown data={periodData?.conversions_by_device} type="Conversions" />
            </CardContent>
          </Card>

          {/* Engagement Metrics */}
          <Card className="lg:col-span-6">
            <CardHeader>
              <CardTitle>Engagement Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <EngagementMetrics currentPeriodData={periodData} previousPeriodData={previousPeriodData} chartData={currentPeriodRecords} />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
