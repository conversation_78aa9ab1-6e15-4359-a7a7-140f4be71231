/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400..700;1,400..700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@200..700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inconsolata:wght@200..900;1,200..900&display=swap');

/* Existing Global Styles */
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&display=swap');

body, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, textarea, select, label {
  font-family: 'Outfit', sans-serif !important;
  color: #2D2E33;
}

.bg-[#717BFF] div, .bg-[#717BFF] div div {
  color: white !important;
}
button.bg-[#717BFF],
button[class*="bg-[#717BFF]"],
button.bg-primary {
  background-color: #717BFF !important;
  color: white !important;
}
button.bg-[#717BFF]:hover,
button[class*="bg-[#717BFF]"]:hover,
button.bg-primary:hover {
  background-color: #6269e3 !important;
}
/* This rule targets any direct children (like span text) of the lavender buttons */
button.bg-[#717BFF] *,
button[class*="bg-[#717BFF]"] *,
button.bg-primary * {
  color: white !important;
}
button.bg-[#717BFF] svg,
button[class*="bg-[#717BFF]"] svg,
button.bg-primary svg {
  color: white !important;
  fill: white !important;
}
.bg-[#2D2E33], .bg-[#3a3b42],
button.bg-[#2D2E33], button.bg-[#3a3b42],
[style*="background-color: #2D2E33"] {
  color: white !important;
}
button.bg-[#2D2E33] *, button.bg-[#3a3b42] *,
[style*="background-color: #2D2E33"] * {
  color: white !important;
}
.text-gray-900 {
  color: #2D2E33 !important;
}
.text-black {
  color: #2D2E33 !important;
}
button.bg-black,
button.bg-[#2D2E33] {
  background-color: #2D2E33 !important;
  color: white !important;
}
button.bg-black:hover,
button.bg-[#2D2E33]:hover {
  background-color: #3a3b42 !important;
}
svg {
  color: #2D2E33;
}
svg.text-[#67B577], svg.text-[#EB8E94], svg.text-[#8890ea],
svg.text-blue-600, svg.text-amber-500, svg.text-green-500,
svg.text-red-600, svg.text-purple-500, svg.text-orange-500 {
  color: currentColor;
}
button.bg-[#2D2E33] svg, button.bg-[#3a3b42] svg,
[style*="background-color: #2D2E33"] svg {
  color: white !important;
}
th, td {
  color: #2D2E33;
}
.font-bold, .font-semibold, .font-medium {
  color: #2D2E33;
}
h1.text-2xl.font-bold.text-gray-900 {
  color: #2D2E33 !important;
}
.card, .shadow-sm, .bg-white {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}
.bg-gray-50, .bg-[#FAFBFC] {
  background-color: #FAFBFC !important;
}
.rounded-xl, .rounded-2xl {
  border-radius: 1rem !important;
}
.font-light { font-weight: 300 !important; }
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }
body { letter-spacing: -0.01em; }
h1, h2, h3, h4, h5, h6 { letter-spacing: -0.02em; }
input[type="checkbox"]:checked { accent-color: #717BFF !important; }
button[role="checkbox"][data-state="checked"] {
  background-color: #717BFF !important;
  color: white !important;
  border-color: #717BFF !important;
}
input[type="radio"]:checked { accent-color: #717BFF !important; }
.enterprise-gradient { background: linear-gradient(135deg, #717BFF 0%, #8890ea 100%); }

/* CSS rules for Quill custom fonts content */
.ql-font-montserrat { font-family: 'Montserrat', sans-serif; }
.ql-font-lato { font-family: 'Lato', sans-serif; }
.ql-font-open_sans { font-family: 'Open Sans', sans-serif; }
.ql-font-merriweather { font-family: 'Merriweather', serif; }
.ql-font-lora { font-family: 'Lora', serif; }
.ql-font-oswald { font-family: 'Oswald', sans-serif; }
.ql-font-inconsolata { font-family: 'Inconsolata', monospace; }

/* CSS rules for Quill toolbar font names */
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="montserrat"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="montserrat"]::before {
  content: 'Montserrat';
  font-family: 'Montserrat', sans-serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="lato"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="lato"]::before {
  content: 'Lato';
  font-family: 'Lato', sans-serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="open_sans"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="open_sans"]::before {
  content: 'Open Sans';
  font-family: 'Open Sans', sans-serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="merriweather"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="merriweather"]::before {
  content: 'Merriweather';
  font-family: 'Merriweather', serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="lora"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="lora"]::before {
  content: 'Lora';
  font-family: 'Lora', serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="oswald"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="oswald"]::before {
  content: 'Oswald';
  font-family: 'Oswald', sans-serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="inconsolata"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="inconsolata"]::before {
  content: 'Inconsolata';
  font-family: 'Inconsolata', monospace;
}
/* Default sans-serif, serif, monospace labels */
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="sans-serif"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="sans-serif"]::before {
    content: 'Sans Serif';
    font-family: sans-serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="serif"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="serif"]::before {
    content: 'Serif';
    font-family: serif;
}
.ql-snow .ql-picker.ql-font .ql-picker-label[data-value="monospace"]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value="monospace"]::before {
    content: 'Monospace';
    font-family: monospace;
}
