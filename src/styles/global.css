/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lora:ital,wght@0,400..700;1,400..700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@200..700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inconsolata:wght@200..900;1,200..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Outfit:wght@300;400;500;600;700&display=swap');

/* Global Typography */
body, h1, h2, h3, h4, h5, h6, p, span, div, a, button, input, textarea, select, label {
  font-family: 'Outfit', sans-serif !important;
  color: #2D2E33;
}

body {
  letter-spacing: -0.01em;
}

h1, h2, h3, h4, h5, h6 {
  letter-spacing: -0.02em;

  &.text-2xl.font-bold.text-gray-900 {
    color: #2D2E33 !important;
  }
}

/* Font Weights */
.font-light { font-weight: 300 !important; }
.font-normal { font-weight: 400 !important; }
.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

/* Color Utilities */
.text-gray-900 {
  color: #2D2E33 !important;
}

.text-black {
  color: #2D2E33 !important;
}

/* Background Colors */
.bg-gray-50, .bg-[#FAFBFC] {
  background-color: #FAFBFC !important;
}

.enterprise-gradient {
  background: linear-gradient(135deg, #717BFF 0%, #8890ea 100%);
}

/* Primary Color Theme (#717BFF) */
.bg-[#717BFF] {
  div, div div {
    color: white !important;
  }
}

button {
  &.bg-[#717BFF],
  &[class*="bg-[#717BFF]"],
  &.bg-primary {
    background-color: #717BFF !important;
    color: white !important;

    &:hover {
      background-color: #6269e3 !important;
    }

    /* Target any direct children (like span text) */
    * {
      color: white !important;
    }

    svg {
      color: white !important;
      fill: white !important;
    }
  }
}

/* Dark Color Theme (#2D2E33) */
.bg-[#2D2E33], .bg-[#3a3b42],
[style*="background-color: #2D2E33"] {
  color: white !important;

  * {
    color: white !important;
  }

  svg {
    color: white !important;
  }
}

button {
  &.bg-[#2D2E33], &.bg-[#3a3b42] {
    color: white !important;

    * {
      color: white !important;
    }

    svg {
      color: white !important;
    }
  }

  &.bg-black,
  &.bg-[#2D2E33] {
    background-color: #2D2E33 !important;
    color: white !important;

    &:hover {
      background-color: #3a3b42 !important;
    }
  }
}

/* SVG Icons */
svg {
  color: #2D2E33;

  &.text-[#67B577], &.text-[#EB8E94], &.text-[#8890ea],
  &.text-blue-600, &.text-amber-500, &.text-green-500,
  &.text-red-600, &.text-purple-500, &.text-orange-500 {
    color: currentColor;
  }
}

/* Table Elements */
th, td {
  color: #2D2E33;
}

.font-bold, .font-semibold, .font-medium {
  color: #2D2E33;
}

/* Layout & Shadows */
.card, .shadow-sm, .bg-white {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.rounded-xl, .rounded-2xl {
  border-radius: 1rem !important;
}

/* Form Controls */
input {
  &[type="checkbox"]:checked {
    accent-color: #717BFF !important;
  }

  &[type="radio"]:checked {
    accent-color: #717BFF !important;
  }
}

button[role="checkbox"][data-state="checked"] {
  background-color: #717BFF !important;
  color: white !important;
  border-color: #717BFF !important;
}

/* Quill Editor Styles */
/* Custom font classes for Quill editor content */
.ql-font-montserrat { font-family: 'Montserrat', sans-serif; }
.ql-font-lato { font-family: 'Lato', sans-serif; }
.ql-font-open_sans { font-family: 'Open Sans', sans-serif; }
.ql-font-merriweather { font-family: 'Merriweather', serif; }
.ql-font-lora { font-family: 'Lora', serif; }
.ql-font-oswald { font-family: 'Oswald', sans-serif; }
.ql-font-inconsolata { font-family: 'Inconsolata', monospace; }

/* Quill toolbar font picker styling */
.ql-snow .ql-picker.ql-font {
  .ql-picker-label, .ql-picker-item {
    &[data-value="montserrat"]::before {
      content: 'Montserrat';
      font-family: 'Montserrat', sans-serif;
    }

    &[data-value="lato"]::before {
      content: 'Lato';
      font-family: 'Lato', sans-serif;
    }

    &[data-value="open_sans"]::before {
      content: 'Open Sans';
      font-family: 'Open Sans', sans-serif;
    }

    &[data-value="merriweather"]::before {
      content: 'Merriweather';
      font-family: 'Merriweather', serif;
    }

    &[data-value="lora"]::before {
      content: 'Lora';
      font-family: 'Lora', serif;
    }

    &[data-value="oswald"]::before {
      content: 'Oswald';
      font-family: 'Oswald', sans-serif;
    }

    &[data-value="inconsolata"]::before {
      content: 'Inconsolata';
      font-family: 'Inconsolata', monospace;
    }

    /* Default font options */
    &[data-value="sans-serif"]::before {
      content: 'Sans Serif';
      font-family: sans-serif;
    }

    &[data-value="serif"]::before {
      content: 'Serif';
      font-family: serif;
    }

    &[data-value="monospace"]::before {
      content: 'Monospace';
      font-family: monospace;
    }
  }
}
