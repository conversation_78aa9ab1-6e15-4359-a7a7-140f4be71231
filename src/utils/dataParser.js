/**
 * Utility functions for parsing JSON string fields in mock data
 * This handles the escaped JSON strings from CSV conversion
 */

/**
 * Safely parse a JSON string field, handling escaped quotes
 * @param {string|object|array} field - The field to parse
 * @param {any} defaultValue - Default value if parsing fails
 * @returns {any} Parsed value or default
 */
export const safeParseJSONField = (field, defaultValue = null) => {
  if (!field) return defaultValue;
  
  // If already parsed, return as-is
  if (typeof field !== 'string') return field;
  
  try {
    // Replace escaped quotes and parse JSON
    const cleanedField = field.replace(/""/g, '"');
    return JSON.parse(cleanedField);
  } catch (error) {
    console.warn('Failed to parse JSON field:', error);
    return defaultValue;
  }
};

/**
 * Parse Google Analytics data with JSON string fields
 * @param {object} rawData - Raw analytics data from API
 * @returns {object} Parsed data with proper objects/arrays
 */
export const parseGoogleAnalyticsData = (rawData) => {
  if (!rawData) return null;
  
  const processedData = { ...rawData };
  
  // Parse all JSON string fields
  const jsonFields = [
    'sessions_by_channel',
    'users_by_channel', 
    'sessions_by_device',
    'users_by_device',
    'sessions_by_source',
    'users_by_source',
    'views_by_page',
    'events_by_type',
    'conversions_by_source',
    'conversions_by_device'
  ];
  
  jsonFields.forEach(field => {
    if (processedData[field]) {
      processedData[field] = safeParseJSONField(processedData[field], []);
    }
  });
  
  return processedData;
};

/**
 * Parse Forecast Model data with JSON string fields
 * @param {object} rawData - Raw forecast data from API
 * @returns {object} Parsed data with proper objects/arrays
 */
export const parseForecastModelData = (rawData) => {
  if (!rawData) return null;
  
  const processedData = { ...rawData };
  
  // Parse JSON string fields
  processedData.predictions = safeParseJSONField(processedData.predictions, []);
  processedData.inputs_used = safeParseJSONField(processedData.inputs_used, []);
  processedData.influencing_factors = safeParseJSONField(processedData.influencing_factors, []);
  processedData.scenario_assumptions = safeParseJSONField(processedData.scenario_assumptions, {});
  
  return processedData;
};

/**
 * Parse Benchmark data with JSON string fields
 * @param {object} rawData - Raw benchmark data from API
 * @returns {object} Parsed data with proper objects/arrays
 */
export const parseBenchmarkData = (rawData) => {
  if (!rawData) return null;
  
  const processedData = { ...rawData };
  
  // Parse JSON string fields
  processedData.metrics = safeParseJSONField(processedData.metrics, getDefaultMetrics());
  processedData.service_distribution = safeParseJSONField(processedData.service_distribution, []);
  processedData.percentiles = safeParseJSONField(processedData.percentiles, {});
  processedData.trends = safeParseJSONField(processedData.trends, {});
  
  return processedData;
};

/**
 * Default metrics structure for benchmark data
 */
const getDefaultMetrics = () => ({
  financial: {
    avg_monthly_revenue: 0,
    revenue_growth_rate: 0,
    profit_margin: 0,
    avg_transaction_value: 0,
    cost_per_acquisition: 0
  },
  operational: {
    provider_utilization: 0,
    room_utilization: 0,
    avg_treatment_time: 0,
    rebook_rate: 0,
    no_show_rate: 0
  },
  marketing: {
    lead_conversion_rate: 0,
    customer_acquisition_cost: 0,
    website_conversion_rate: 0,
    social_engagement_rate: 0,
    referral_rate: 0
  },
  customer: {
    retention_rate: 0,
    lifetime_value: 0,
    satisfaction_score: 0,
    avg_treatments_per_patient: 0,
    repeat_visit_rate: 0
  }
});

/**
 * Generic function to parse any data object with potential JSON string fields
 * @param {object} rawData - Raw data object
 * @param {string[]} jsonFields - Array of field names that might contain JSON strings
 * @param {object} defaultValues - Default values for each field
 * @returns {object} Parsed data
 */
export const parseDataWithJSONFields = (rawData, jsonFields = [], defaultValues = {}) => {
  if (!rawData) return null;
  
  const processedData = { ...rawData };
  
  jsonFields.forEach(field => {
    if (processedData[field]) {
      processedData[field] = safeParseJSONField(
        processedData[field], 
        defaultValues[field] || null
      );
    }
  });
  
  return processedData;
};

/**
 * Safe property access helper for nested objects
 * @param {object} obj - Object to access
 * @param {string} path - Dot-separated path (e.g., 'metrics.financial.revenue')
 * @param {any} defaultValue - Default value if path doesn't exist
 * @returns {any} Value at path or default
 */
export const safeGet = (obj, path, defaultValue = null) => {
  if (!obj || !path) return defaultValue;
  
  const pathArray = path.split('.');
  let current = obj;
  
  for (const key of pathArray) {
    if (current && typeof current === 'object' && key in current) {
      current = current[key];
    } else {
      return defaultValue;
    }
  }
  
  return current !== null && current !== undefined ? current : defaultValue;
};
